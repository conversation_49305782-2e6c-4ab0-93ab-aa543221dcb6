/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/terms-of-service/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cankkorwoo%5C%5Cankkor%5C%5Csrc%5C%5Capp%5C%5Cterms-of-service%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cankkorwoo%5C%5Cankkor%5C%5Csrc%5C%5Capp%5C%5Cterms-of-service%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \******************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/terms-of-service/page.tsx */ \"(app-pages-browser)/./src/app/terms-of-service/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q2Fua2tvcndvbyU1QyU1Q2Fua2tvciU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3Rlcm1zLW9mLXNlcnZpY2UlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLGdNQUFnRyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvPzM2ODUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFxhbmtrb3J3b29cXFxcYW5ra29yXFxcXHNyY1xcXFxhcHBcXFxcdGVybXMtb2Ytc2VydmljZVxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cankkorwoo%5C%5Cankkor%5C%5Csrc%5C%5Capp%5C%5Cterms-of-service%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/anchor.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/anchor.js ***!
  \************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Anchor; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */ \nconst Anchor = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Anchor\", [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"5\",\n            r: \"3\",\n            key: \"rqqgnr\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"12\",\n            x2: \"12\",\n            y1: \"22\",\n            y2: \"8\",\n            key: \"abakz7\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M5 12H2a10 10 0 0 0 20 0h-3\",\n            key: \"1hv3nh\"\n        }\n    ]\n]);\n //# sourceMappingURL=anchor.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/anchor.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/terms-of-service/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/terms-of-service/page.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TermsOfServicePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_Anchor_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Anchor!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/anchor.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction TermsOfServicePage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-[#f8f8f5] min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative bg-[#2c2c27] h-[30vh] flex items-center justify-center overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[#2c2c27]/90\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[url('https://images.unsplash.com/photo-1453928582365-b6ad33cbcf64?q=80&w=2673&auto=format&fit=crop')] bg-cover bg-center opacity-20\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8,\n                            ease: [\n                                0.22,\n                                1,\n                                0.36,\n                                1\n                            ]\n                        },\n                        className: \"container mx-auto px-4 text-center relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"font-serif text-4xl md:text-5xl text-[#f8f8f5] mb-4\",\n                                children: \"Terms of Service\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-[1px] w-24 bg-[#8a8778] mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-[#e5e2d9] max-w-2xl mx-auto\",\n                                children: \"Our commitment to transparency and fairness\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-16 md:py-24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.2\n                    },\n                    className: \"max-w-4xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"prose prose-lg max-w-none text-[#5c5c52]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-12 p-8 bg-[#f4f3f0] border-l-4 border-[#8a8778]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"italic text-[#2c2c27]\",\n                                    children: \"Welcome to ANKKOR. These Terms of Service outline the rules and regulations for using our website and services. By placing an order or interacting with us on our website, WhatsApp, or Instagram, you agree to the following terms.\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-serif text-2xl text-[#2c2c27] mt-12 mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"h-px w-10 bg-[#8a8778] mr-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"About ANKKOR\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mb-6\",\n                                children: \"ANKKOR is a formal clothing and accessories brand based in Ludhiana, Punjab. Our products are designed for anyone who appreciates classy, old-money-inspired fashion. Currently, we offer formal shirts, with polos, pants, and accessories launching soon.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-serif text-2xl text-[#2c2c27] mt-12 mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"h-px w-10 bg-[#8a8778] mr-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Eligibility & Ordering\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mb-6\",\n                                children: \"You must be at least 18 years old to place an order. If you're under 18, please use the platform under parental guidance. Orders can be placed through our website, WhatsApp, or Instagram. We accept both prepaid and cash-on-delivery (COD) payments, with secure transactions powered by Razorpay and PayPal.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-serif text-2xl text-[#2c2c27] mt-12 mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"h-px w-10 bg-[#8a8778] mr-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Shipping & Delivery\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mb-6\",\n                                children: \"We deliver across India through our shipping partners DTDC and Dehlivery. Orders are typically fulfilled within 5–7 business days. Shipping charges vary based on the delivery location and will be clearly displayed at checkout.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"my-16 border-t border-b border-[#e5e2d9] py-8 px-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 rounded-full border border-[#8a8778] flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Anchor_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"h-6 w-6 text-[#8a8778]\",\n                                                strokeWidth: 1.5\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                                lineNumber: 69,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-center text-lg font-serif italic text-[#2c2c27]\",\n                                        children: '\"ANKKOR – Where class meets character. Wear timeless. Be anchored.\"'\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-serif text-2xl text-[#2c2c27] mt-12 mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"h-px w-10 bg-[#8a8778] mr-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Exchange Policy\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mb-6\",\n                                children: \"Our exchange policy allows for a 7-day window from the date of delivery. Products must be unused and returned in their original packaging. We do not offer refunds; however, customers can choose to exchange the item or receive the value as redeemable ANKKOR Points through our website.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-serif text-2xl text-[#2c2c27] mt-12 mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"h-px w-10 bg-[#8a8778] mr-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Platform Usage & Conduct\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mb-6\",\n                                children: \"All users are expected to use the platform respectfully. Any misuse, including spamming, fraudulent returns, or unauthorized resale, may result in restricted access to our services. The content, designs, and brand identity of ANKKOR are protected by intellectual property rights. Reproduction or use of any material without written permission is strictly prohibited.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-serif text-2xl text-[#2c2c27] mt-12 mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"h-px w-10 bg-[#8a8778] mr-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Limitation of Liability\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mb-6\",\n                                children: \"While we aim to provide timely service, we are not liable for delays caused by third-party service providers or unforeseen events. ANKKOR reserves the right to suspend or terminate any user account in case of policy violations.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-serif text-2xl text-[#2c2c27] mt-12 mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"h-px w-10 bg-[#8a8778] mr-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Governing Law\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mb-6\",\n                                children: \"All transactions and disputes are governed by Indian laws under the jurisdiction of Ludhiana, Punjab.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-16 pt-8 border-t border-[#e5e2d9]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:flex-row md:justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-[#8a8778] mb-4 md:mb-0\",\n                                            children: \"Last Updated: April 2025\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: \"/privacy-policy\",\n                                                    className: \"text-sm text-[#2c2c27] hover:text-[#8a8778] transition-colors\",\n                                                    children: \"Privacy Policy\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: \"/shipping-policy\",\n                                                    className: \"text-sm text-[#2c2c27] hover:text-[#8a8778] transition-colors\",\n                                                    children: \"Shipping Policy\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    href: \"/return-policy\",\n                                                    className: \"text-sm text-[#2c2c27] hover:text-[#8a8778] transition-colors\",\n                                                    children: \"Return Policy\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\terms-of-service\\\\page.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n_c = TermsOfServicePage;\nvar _c;\n$RefreshReg$(_c, \"TermsOfServicePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/terms-of-service/page.tsx\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["framework-node_modules_next_dist_a","framework-node_modules_next_dist_client_a","framework-node_modules_next_dist_client_components_ap","framework-node_modules_next_dist_client_components_b","framework-node_modules_next_dist_client_components_layout-router_js-4906aef6","framework-node_modules_next_dist_client_components_m","framework-node_modules_next_dist_client_components_p","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_C","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_LeftRightDi-d5fdd2e0","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_O","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_Overlay_mai-e776ae3b","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_Te","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_V","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_container_B","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_container_R","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_helpers_f","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_helpers_h","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_h","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_styles_B","framework-node_modules_next_dist_client_components_rea","framework-node_modules_next_dist_client_components_re","framework-node_modules_next_dist_client_components_router-reducer_co","framework-node_modules_next_dist_client_components_router-reducer_fe","framework-node_modules_next_dist_client_components_router-reducer_h","framework-node_modules_next_dist_client_components_router-reducer_pp","framework-node_modules_next_dist_client_components_router-reducer_reducers_f","framework-node_modules_next_dist_client_components_router-reducer_reducers_r","framework-node_modules_next_dist_client_components_router-reducer_r","framework-node_modules_next_dist_client_c","framework-node_modules_next_dist_client_g","framework-node_modules_next_dist_client_l","framework-node_modules_next_dist_compiled_a","framework-node_modules_next_dist_compiled_m","framework-node_modules_next_dist_compiled_react-dom_cjs_react-dom_development_js-3041f41d","framework-node_modules_next_dist_compiled_react-d","framework-node_modules_next_dist_compiled_react-server-dom-webpack_cjs_react-server-dom-webpack-clie-4912d8da","framework-node_modules_next_dist_compiled_react_cjs_react-jsx-dev-runtime_development_js-12999a20","framework-node_modules_next_dist_compiled_react_c","framework-node_modules_next_dist_compiled_react_cjs_react_development_js-a784779d","framework-node_modules_next_dist_compiled_r","framework-node_modules_next_dist_l","framework-node_modules_next_dist_shared_lib_a","framework-node_modules_next_dist_shared_lib_ha","framework-node_modules_next_dist_shared_lib_h","framework-node_modules_next_dist_shared_lib_lazy-dynamic_b","framework-node_modules_next_dist_shared_lib_m","framework-node_modules_next_dist_shared_lib_router-","framework-node_modules_next_dist_shared_lib_router_utils_o","framework-node_modules_next_dist_shared_lib_r","framework-node_modules_next_d","framework-node_modules_next_font_google_target_css-0","commons-_","commons-node_modules_framer-motion_dist_es_animation_animators_i","commons-node_modules_framer-motion_dist_es_a","commons-node_modules_framer-motion_dist_es_d","commons-node_modules_framer-motion_dist_es_motion_f","commons-node_modules_framer-motion_dist_es_projection_a","commons-node_modules_framer-motion_dist_es_projection_node_create-projection-node_mjs-d9cf742e","commons-node_modules_framer-motion_dist_es_render_VisualElement_mjs-19d9658a","commons-node_modules_framer-motion_dist_es_render_d","commons-node_modules_framer-motion_dist_es_r","commons-node_modules_framer-motion_dist_es_value_i","commons-node_modules_go","commons-node_modules_graphql_language_a","commons-node_modules_graphql_language_parser_mjs-c45803c0","commons-node_modules_graphql_language_p","commons-node_modules_l","commons-node_modules_react-hook-form_dist_index_esm_mjs-74baa987","commons-node_modules_tailwind-merge_dist_bundle-mjs_mjs-a19ea93e","commons-node_modules_upstash_redis_chunk-5XANP4AV_mjs-ec81489a","commons-node_modules_zustand_esm_i","commons-src_components_auth_AuthForm_tsx-6dd93bd8","commons-src_components_c","commons-src_com","commons-src_lib_c","commons-src_lib_s","commons-src_lib_woocommerce_ts-ea0e4c9f","main-app"], function() { return __webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cankkorwoo%5C%5Cankkor%5C%5Csrc%5C%5Capp%5C%5Cterms-of-service%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);