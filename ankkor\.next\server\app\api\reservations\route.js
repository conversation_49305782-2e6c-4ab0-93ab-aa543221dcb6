"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/reservations/route";
exports.ids = ["app/api/reservations/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Freservations%2Froute&page=%2Fapi%2Freservations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Freservations%2Froute.ts&appDir=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cankkorwoo%5Cankkor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Freservations%2Froute&page=%2Fapi%2Freservations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Freservations%2Froute.ts&appDir=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cankkorwoo%5Cankkor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var E_ankkorwoo_ankkor_src_app_api_reservations_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/reservations/route.ts */ \"(rsc)/./src/app/api/reservations/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/reservations/route\",\n        pathname: \"/api/reservations\",\n        filename: \"route\",\n        bundlePath: \"app/api/reservations/route\"\n    },\n    resolvedPagePath: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\api\\\\reservations\\\\route.ts\",\n    nextConfigOutput,\n    userland: E_ankkorwoo_ankkor_src_app_api_reservations_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/reservations/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Freservations%2Froute&page=%2Fapi%2Freservations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Freservations%2Froute.ts&appDir=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cankkorwoo%5Cankkor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/reservations/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/reservations/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_stockReservation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/stockReservation */ \"(rsc)/./src/lib/stockReservation.ts\");\n\n\n// POST - Create a new stock reservation\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { productId, quantity, userId, variationId, cartId, action } = body;\n        // Handle different actions\n        switch(action){\n            case \"create\":\n                if (!productId || !quantity || !userId) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: \"Missing required fields: productId, quantity, userId\"\n                    }, {\n                        status: 400\n                    });\n                }\n                const result = await (0,_lib_stockReservation__WEBPACK_IMPORTED_MODULE_1__.createStockReservation)(productId, parseInt(quantity), userId, variationId, cartId);\n                if (result.success) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: true,\n                        reservation: result.reservation,\n                        message: `Reserved ${quantity} items for ${15} minutes`\n                    });\n                } else {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: result.error,\n                        availableStock: result.availableStock\n                    }, {\n                        status: 400\n                    });\n                }\n            case \"confirm\":\n                if (!body.reservationId) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: \"Missing reservationId\"\n                    }, {\n                        status: 400\n                    });\n                }\n                const confirmed = await (0,_lib_stockReservation__WEBPACK_IMPORTED_MODULE_1__.confirmReservation)(body.reservationId);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: confirmed,\n                    message: confirmed ? \"Reservation confirmed\" : \"Failed to confirm reservation\"\n                });\n            case \"release\":\n                if (!body.reservationId) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: \"Missing reservationId\"\n                    }, {\n                        status: 400\n                    });\n                }\n                const released = await (0,_lib_stockReservation__WEBPACK_IMPORTED_MODULE_1__.releaseReservation)(body.reservationId);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: released,\n                    message: released ? \"Reservation released\" : \"Failed to release reservation\"\n                });\n            case \"cleanup\":\n                const cleanedUp = await (0,_lib_stockReservation__WEBPACK_IMPORTED_MODULE_1__.cleanupExpiredReservations)();\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    cleanedUp,\n                    message: `Cleaned up ${cleanedUp} expired reservations`\n                });\n            default:\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Invalid action. Use: create, confirm, release, or cleanup\"\n                }, {\n                    status: 400\n                });\n        }\n    } catch (error) {\n        console.error(\"Error in reservations API:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// GET - Get user reservations or check stock availability\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const action = searchParams.get(\"action\");\n        const userId = searchParams.get(\"userId\");\n        const productId = searchParams.get(\"productId\");\n        const variationId = searchParams.get(\"variationId\");\n        switch(action){\n            case \"user_reservations\":\n                if (!userId) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: \"Missing userId parameter\"\n                    }, {\n                        status: 400\n                    });\n                }\n                const reservations = await (0,_lib_stockReservation__WEBPACK_IMPORTED_MODULE_1__.getUserActiveReservations)(userId);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    reservations,\n                    count: reservations.length\n                });\n            case \"check_stock\":\n                if (!productId) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: \"Missing productId parameter\"\n                    }, {\n                        status: 400\n                    });\n                }\n                const stockCheck = await (0,_lib_stockReservation__WEBPACK_IMPORTED_MODULE_1__.checkAvailableStock)(productId, variationId || undefined);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: stockCheck.success,\n                    availableStock: stockCheck.availableStock,\n                    totalStock: stockCheck.totalStock,\n                    reservedStock: stockCheck.reservedStock\n                });\n            default:\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    message: \"Stock Reservation API\",\n                    endpoints: {\n                        \"POST /api/reservations\": {\n                            actions: [\n                                \"create\",\n                                \"confirm\",\n                                \"release\",\n                                \"cleanup\"\n                            ],\n                            description: \"Manage stock reservations\"\n                        },\n                        \"GET /api/reservations?action=user_reservations&userId=X\": \"Get user reservations\",\n                        \"GET /api/reservations?action=check_stock&productId=X\": \"Check available stock\"\n                    }\n                });\n        }\n    } catch (error) {\n        console.error(\"Error in reservations GET API:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE - Release specific reservation\nasync function DELETE(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const reservationId = searchParams.get(\"reservationId\");\n        if (!reservationId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Missing reservationId parameter\"\n            }, {\n                status: 400\n            });\n        }\n        const released = await (0,_lib_stockReservation__WEBPACK_IMPORTED_MODULE_1__.releaseReservation)(reservationId);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: released,\n            message: released ? \"Reservation released\" : \"Failed to release reservation\"\n        });\n    } catch (error) {\n        console.error(\"Error in reservations DELETE API:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9yZXNlcnZhdGlvbnMvcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBd0Q7QUFReEI7QUFFaEMsd0NBQXdDO0FBQ2pDLGVBQWVPLEtBQUtDLE9BQW9CO0lBQzdDLElBQUk7UUFDRixNQUFNQyxPQUFPLE1BQU1ELFFBQVFFLElBQUk7UUFDL0IsTUFBTSxFQUFFQyxTQUFTLEVBQUVDLFFBQVEsRUFBRUMsTUFBTSxFQUFFQyxXQUFXLEVBQUVDLE1BQU0sRUFBRUMsTUFBTSxFQUFFLEdBQUdQO1FBRXJFLDJCQUEyQjtRQUMzQixPQUFRTztZQUNOLEtBQUs7Z0JBQ0gsSUFBSSxDQUFDTCxhQUFhLENBQUNDLFlBQVksQ0FBQ0MsUUFBUTtvQkFDdEMsT0FBT2IscURBQVlBLENBQUNVLElBQUksQ0FDdEI7d0JBQUVPLE9BQU87b0JBQXVELEdBQ2hFO3dCQUFFQyxRQUFRO29CQUFJO2dCQUVsQjtnQkFFQSxNQUFNQyxTQUFTLE1BQU1sQiw2RUFBc0JBLENBQ3pDVSxXQUNBUyxTQUFTUixXQUNUQyxRQUNBQyxhQUNBQztnQkFHRixJQUFJSSxPQUFPRSxPQUFPLEVBQUU7b0JBQ2xCLE9BQU9yQixxREFBWUEsQ0FBQ1UsSUFBSSxDQUFDO3dCQUN2QlcsU0FBUzt3QkFDVEMsYUFBYUgsT0FBT0csV0FBVzt3QkFDL0JDLFNBQVMsQ0FBQyxTQUFTLEVBQUVYLFNBQVMsV0FBVyxFQUFFLEdBQUcsUUFBUSxDQUFDO29CQUN6RDtnQkFDRixPQUFPO29CQUNMLE9BQU9aLHFEQUFZQSxDQUFDVSxJQUFJLENBQ3RCO3dCQUFFTyxPQUFPRSxPQUFPRixLQUFLO3dCQUFFTyxnQkFBZ0JMLE9BQU9LLGNBQWM7b0JBQUMsR0FDN0Q7d0JBQUVOLFFBQVE7b0JBQUk7Z0JBRWxCO1lBRUYsS0FBSztnQkFDSCxJQUFJLENBQUNULEtBQUtnQixhQUFhLEVBQUU7b0JBQ3ZCLE9BQU96QixxREFBWUEsQ0FBQ1UsSUFBSSxDQUN0Qjt3QkFBRU8sT0FBTztvQkFBd0IsR0FDakM7d0JBQUVDLFFBQVE7b0JBQUk7Z0JBRWxCO2dCQUVBLE1BQU1RLFlBQVksTUFBTXRCLHlFQUFrQkEsQ0FBQ0ssS0FBS2dCLGFBQWE7Z0JBQzdELE9BQU96QixxREFBWUEsQ0FBQ1UsSUFBSSxDQUFDO29CQUN2QlcsU0FBU0s7b0JBQ1RILFNBQVNHLFlBQVksMEJBQTBCO2dCQUNqRDtZQUVGLEtBQUs7Z0JBQ0gsSUFBSSxDQUFDakIsS0FBS2dCLGFBQWEsRUFBRTtvQkFDdkIsT0FBT3pCLHFEQUFZQSxDQUFDVSxJQUFJLENBQ3RCO3dCQUFFTyxPQUFPO29CQUF3QixHQUNqQzt3QkFBRUMsUUFBUTtvQkFBSTtnQkFFbEI7Z0JBRUEsTUFBTVMsV0FBVyxNQUFNdEIseUVBQWtCQSxDQUFDSSxLQUFLZ0IsYUFBYTtnQkFDNUQsT0FBT3pCLHFEQUFZQSxDQUFDVSxJQUFJLENBQUM7b0JBQ3ZCVyxTQUFTTTtvQkFDVEosU0FBU0ksV0FBVyx5QkFBeUI7Z0JBQy9DO1lBRUYsS0FBSztnQkFDSCxNQUFNQyxZQUFZLE1BQU10QixpRkFBMEJBO2dCQUNsRCxPQUFPTixxREFBWUEsQ0FBQ1UsSUFBSSxDQUFDO29CQUN2QlcsU0FBUztvQkFDVE87b0JBQ0FMLFNBQVMsQ0FBQyxXQUFXLEVBQUVLLFVBQVUscUJBQXFCLENBQUM7Z0JBQ3pEO1lBRUY7Z0JBQ0UsT0FBTzVCLHFEQUFZQSxDQUFDVSxJQUFJLENBQ3RCO29CQUFFTyxPQUFPO2dCQUE0RCxHQUNyRTtvQkFBRUMsUUFBUTtnQkFBSTtRQUVwQjtJQUVGLEVBQUUsT0FBT0QsT0FBTztRQUNkWSxRQUFRWixLQUFLLENBQUMsOEJBQThCQTtRQUM1QyxPQUFPakIscURBQVlBLENBQUNVLElBQUksQ0FDdEI7WUFBRU8sT0FBTztRQUF3QixHQUNqQztZQUFFQyxRQUFRO1FBQUk7SUFFbEI7QUFDRjtBQUVBLDBEQUEwRDtBQUNuRCxlQUFlWSxJQUFJdEIsT0FBb0I7SUFDNUMsSUFBSTtRQUNGLE1BQU0sRUFBRXVCLFlBQVksRUFBRSxHQUFHLElBQUlDLElBQUl4QixRQUFReUIsR0FBRztRQUM1QyxNQUFNakIsU0FBU2UsYUFBYUcsR0FBRyxDQUFDO1FBQ2hDLE1BQU1yQixTQUFTa0IsYUFBYUcsR0FBRyxDQUFDO1FBQ2hDLE1BQU12QixZQUFZb0IsYUFBYUcsR0FBRyxDQUFDO1FBQ25DLE1BQU1wQixjQUFjaUIsYUFBYUcsR0FBRyxDQUFDO1FBRXJDLE9BQVFsQjtZQUNOLEtBQUs7Z0JBQ0gsSUFBSSxDQUFDSCxRQUFRO29CQUNYLE9BQU9iLHFEQUFZQSxDQUFDVSxJQUFJLENBQ3RCO3dCQUFFTyxPQUFPO29CQUEyQixHQUNwQzt3QkFBRUMsUUFBUTtvQkFBSTtnQkFFbEI7Z0JBRUEsTUFBTWlCLGVBQWUsTUFBTWhDLGdGQUF5QkEsQ0FBQ1U7Z0JBQ3JELE9BQU9iLHFEQUFZQSxDQUFDVSxJQUFJLENBQUM7b0JBQ3ZCVyxTQUFTO29CQUNUYztvQkFDQUMsT0FBT0QsYUFBYUUsTUFBTTtnQkFDNUI7WUFFRixLQUFLO2dCQUNILElBQUksQ0FBQzFCLFdBQVc7b0JBQ2QsT0FBT1gscURBQVlBLENBQUNVLElBQUksQ0FDdEI7d0JBQUVPLE9BQU87b0JBQThCLEdBQ3ZDO3dCQUFFQyxRQUFRO29CQUFJO2dCQUVsQjtnQkFFQSxNQUFNb0IsYUFBYSxNQUFNcEMsMEVBQW1CQSxDQUFDUyxXQUFXRyxlQUFleUI7Z0JBQ3ZFLE9BQU92QyxxREFBWUEsQ0FBQ1UsSUFBSSxDQUFDO29CQUN2QlcsU0FBU2lCLFdBQVdqQixPQUFPO29CQUMzQkcsZ0JBQWdCYyxXQUFXZCxjQUFjO29CQUN6Q2dCLFlBQVlGLFdBQVdFLFVBQVU7b0JBQ2pDQyxlQUFlSCxXQUFXRyxhQUFhO2dCQUN6QztZQUVGO2dCQUNFLE9BQU96QyxxREFBWUEsQ0FBQ1UsSUFBSSxDQUFDO29CQUN2QmEsU0FBUztvQkFDVG1CLFdBQVc7d0JBQ1QsMEJBQTBCOzRCQUN4QkMsU0FBUztnQ0FBQztnQ0FBVTtnQ0FBVztnQ0FBVzs2QkFBVTs0QkFDcERDLGFBQWE7d0JBQ2Y7d0JBQ0EsMkRBQTJEO3dCQUMzRCx3REFBd0Q7b0JBQzFEO2dCQUNGO1FBQ0o7SUFFRixFQUFFLE9BQU8zQixPQUFPO1FBQ2RZLFFBQVFaLEtBQUssQ0FBQyxrQ0FBa0NBO1FBQ2hELE9BQU9qQixxREFBWUEsQ0FBQ1UsSUFBSSxDQUN0QjtZQUFFTyxPQUFPO1FBQXdCLEdBQ2pDO1lBQUVDLFFBQVE7UUFBSTtJQUVsQjtBQUNGO0FBRUEsd0NBQXdDO0FBQ2pDLGVBQWUyQixPQUFPckMsT0FBb0I7SUFDL0MsSUFBSTtRQUNGLE1BQU0sRUFBRXVCLFlBQVksRUFBRSxHQUFHLElBQUlDLElBQUl4QixRQUFReUIsR0FBRztRQUM1QyxNQUFNUixnQkFBZ0JNLGFBQWFHLEdBQUcsQ0FBQztRQUV2QyxJQUFJLENBQUNULGVBQWU7WUFDbEIsT0FBT3pCLHFEQUFZQSxDQUFDVSxJQUFJLENBQ3RCO2dCQUFFTyxPQUFPO1lBQWtDLEdBQzNDO2dCQUFFQyxRQUFRO1lBQUk7UUFFbEI7UUFFQSxNQUFNUyxXQUFXLE1BQU10Qix5RUFBa0JBLENBQUNvQjtRQUMxQyxPQUFPekIscURBQVlBLENBQUNVLElBQUksQ0FBQztZQUN2QlcsU0FBU007WUFDVEosU0FBU0ksV0FBVyx5QkFBeUI7UUFDL0M7SUFFRixFQUFFLE9BQU9WLE9BQU87UUFDZFksUUFBUVosS0FBSyxDQUFDLHFDQUFxQ0E7UUFDbkQsT0FBT2pCLHFEQUFZQSxDQUFDVSxJQUFJLENBQ3RCO1lBQUVPLE9BQU87UUFBd0IsR0FDakM7WUFBRUMsUUFBUTtRQUFJO0lBRWxCO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hbmtrb3IvLi9zcmMvYXBwL2FwaS9yZXNlcnZhdGlvbnMvcm91dGUudHM/ZDIzMyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xuaW1wb3J0IHsgXG4gIGNyZWF0ZVN0b2NrUmVzZXJ2YXRpb24sIFxuICBjaGVja0F2YWlsYWJsZVN0b2NrLCBcbiAgZ2V0VXNlckFjdGl2ZVJlc2VydmF0aW9ucyxcbiAgY29uZmlybVJlc2VydmF0aW9uLFxuICByZWxlYXNlUmVzZXJ2YXRpb24sXG4gIGNsZWFudXBFeHBpcmVkUmVzZXJ2YXRpb25zXG59IGZyb20gJ0AvbGliL3N0b2NrUmVzZXJ2YXRpb24nO1xuXG4vLyBQT1NUIC0gQ3JlYXRlIGEgbmV3IHN0b2NrIHJlc2VydmF0aW9uXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gUE9TVChyZXF1ZXN0OiBOZXh0UmVxdWVzdCkge1xuICB0cnkge1xuICAgIGNvbnN0IGJvZHkgPSBhd2FpdCByZXF1ZXN0Lmpzb24oKTtcbiAgICBjb25zdCB7IHByb2R1Y3RJZCwgcXVhbnRpdHksIHVzZXJJZCwgdmFyaWF0aW9uSWQsIGNhcnRJZCwgYWN0aW9uIH0gPSBib2R5O1xuXG4gICAgLy8gSGFuZGxlIGRpZmZlcmVudCBhY3Rpb25zXG4gICAgc3dpdGNoIChhY3Rpb24pIHtcbiAgICAgIGNhc2UgJ2NyZWF0ZSc6XG4gICAgICAgIGlmICghcHJvZHVjdElkIHx8ICFxdWFudGl0eSB8fCAhdXNlcklkKSB7XG4gICAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICAgICAgeyBlcnJvcjogJ01pc3NpbmcgcmVxdWlyZWQgZmllbGRzOiBwcm9kdWN0SWQsIHF1YW50aXR5LCB1c2VySWQnIH0sXG4gICAgICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgICAgICApO1xuICAgICAgICB9XG5cbiAgICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgY3JlYXRlU3RvY2tSZXNlcnZhdGlvbihcbiAgICAgICAgICBwcm9kdWN0SWQsXG4gICAgICAgICAgcGFyc2VJbnQocXVhbnRpdHkpLFxuICAgICAgICAgIHVzZXJJZCxcbiAgICAgICAgICB2YXJpYXRpb25JZCxcbiAgICAgICAgICBjYXJ0SWRcbiAgICAgICAgKTtcblxuICAgICAgICBpZiAocmVzdWx0LnN1Y2Nlc3MpIHtcbiAgICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgICAgICAgc3VjY2VzczogdHJ1ZSxcbiAgICAgICAgICAgIHJlc2VydmF0aW9uOiByZXN1bHQucmVzZXJ2YXRpb24sXG4gICAgICAgICAgICBtZXNzYWdlOiBgUmVzZXJ2ZWQgJHtxdWFudGl0eX0gaXRlbXMgZm9yICR7MTV9IG1pbnV0ZXNgXG4gICAgICAgICAgfSk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICAgICAgeyBlcnJvcjogcmVzdWx0LmVycm9yLCBhdmFpbGFibGVTdG9jazogcmVzdWx0LmF2YWlsYWJsZVN0b2NrIH0sXG4gICAgICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgICAgICApO1xuICAgICAgICB9XG5cbiAgICAgIGNhc2UgJ2NvbmZpcm0nOlxuICAgICAgICBpZiAoIWJvZHkucmVzZXJ2YXRpb25JZCkge1xuICAgICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgICAgIHsgZXJyb3I6ICdNaXNzaW5nIHJlc2VydmF0aW9uSWQnIH0sXG4gICAgICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgICAgICApO1xuICAgICAgICB9XG5cbiAgICAgICAgY29uc3QgY29uZmlybWVkID0gYXdhaXQgY29uZmlybVJlc2VydmF0aW9uKGJvZHkucmVzZXJ2YXRpb25JZCk7XG4gICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICAgICAgc3VjY2VzczogY29uZmlybWVkLFxuICAgICAgICAgIG1lc3NhZ2U6IGNvbmZpcm1lZCA/ICdSZXNlcnZhdGlvbiBjb25maXJtZWQnIDogJ0ZhaWxlZCB0byBjb25maXJtIHJlc2VydmF0aW9uJ1xuICAgICAgICB9KTtcblxuICAgICAgY2FzZSAncmVsZWFzZSc6XG4gICAgICAgIGlmICghYm9keS5yZXNlcnZhdGlvbklkKSB7XG4gICAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICAgICAgeyBlcnJvcjogJ01pc3NpbmcgcmVzZXJ2YXRpb25JZCcgfSxcbiAgICAgICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgICAgICk7XG4gICAgICAgIH1cblxuICAgICAgICBjb25zdCByZWxlYXNlZCA9IGF3YWl0IHJlbGVhc2VSZXNlcnZhdGlvbihib2R5LnJlc2VydmF0aW9uSWQpO1xuICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgICAgIHN1Y2Nlc3M6IHJlbGVhc2VkLFxuICAgICAgICAgIG1lc3NhZ2U6IHJlbGVhc2VkID8gJ1Jlc2VydmF0aW9uIHJlbGVhc2VkJyA6ICdGYWlsZWQgdG8gcmVsZWFzZSByZXNlcnZhdGlvbidcbiAgICAgICAgfSk7XG5cbiAgICAgIGNhc2UgJ2NsZWFudXAnOlxuICAgICAgICBjb25zdCBjbGVhbmVkVXAgPSBhd2FpdCBjbGVhbnVwRXhwaXJlZFJlc2VydmF0aW9ucygpO1xuICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICAgICAgY2xlYW5lZFVwLFxuICAgICAgICAgIG1lc3NhZ2U6IGBDbGVhbmVkIHVwICR7Y2xlYW5lZFVwfSBleHBpcmVkIHJlc2VydmF0aW9uc2BcbiAgICAgICAgfSk7XG5cbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgICB7IGVycm9yOiAnSW52YWxpZCBhY3Rpb24uIFVzZTogY3JlYXRlLCBjb25maXJtLCByZWxlYXNlLCBvciBjbGVhbnVwJyB9LFxuICAgICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgICApO1xuICAgIH1cblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGluIHJlc2VydmF0aW9ucyBBUEk6JywgZXJyb3IpO1xuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgZXJyb3I6ICdJbnRlcm5hbCBzZXJ2ZXIgZXJyb3InIH0sXG4gICAgICB7IHN0YXR1czogNTAwIH1cbiAgICApO1xuICB9XG59XG5cbi8vIEdFVCAtIEdldCB1c2VyIHJlc2VydmF0aW9ucyBvciBjaGVjayBzdG9jayBhdmFpbGFiaWxpdHlcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBHRVQocmVxdWVzdDogTmV4dFJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCB7IHNlYXJjaFBhcmFtcyB9ID0gbmV3IFVSTChyZXF1ZXN0LnVybCk7XG4gICAgY29uc3QgYWN0aW9uID0gc2VhcmNoUGFyYW1zLmdldCgnYWN0aW9uJyk7XG4gICAgY29uc3QgdXNlcklkID0gc2VhcmNoUGFyYW1zLmdldCgndXNlcklkJyk7XG4gICAgY29uc3QgcHJvZHVjdElkID0gc2VhcmNoUGFyYW1zLmdldCgncHJvZHVjdElkJyk7XG4gICAgY29uc3QgdmFyaWF0aW9uSWQgPSBzZWFyY2hQYXJhbXMuZ2V0KCd2YXJpYXRpb25JZCcpO1xuXG4gICAgc3dpdGNoIChhY3Rpb24pIHtcbiAgICAgIGNhc2UgJ3VzZXJfcmVzZXJ2YXRpb25zJzpcbiAgICAgICAgaWYgKCF1c2VySWQpIHtcbiAgICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgICAgICB7IGVycm9yOiAnTWlzc2luZyB1c2VySWQgcGFyYW1ldGVyJyB9LFxuICAgICAgICAgICAgeyBzdGF0dXM6IDQwMCB9XG4gICAgICAgICAgKTtcbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnN0IHJlc2VydmF0aW9ucyA9IGF3YWl0IGdldFVzZXJBY3RpdmVSZXNlcnZhdGlvbnModXNlcklkKTtcbiAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICAgIHJlc2VydmF0aW9ucyxcbiAgICAgICAgICBjb3VudDogcmVzZXJ2YXRpb25zLmxlbmd0aFxuICAgICAgICB9KTtcblxuICAgICAgY2FzZSAnY2hlY2tfc3RvY2snOlxuICAgICAgICBpZiAoIXByb2R1Y3RJZCkge1xuICAgICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgICAgIHsgZXJyb3I6ICdNaXNzaW5nIHByb2R1Y3RJZCBwYXJhbWV0ZXInIH0sXG4gICAgICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgICAgICApO1xuICAgICAgICB9XG5cbiAgICAgICAgY29uc3Qgc3RvY2tDaGVjayA9IGF3YWl0IGNoZWNrQXZhaWxhYmxlU3RvY2socHJvZHVjdElkLCB2YXJpYXRpb25JZCB8fCB1bmRlZmluZWQpO1xuICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgICAgIHN1Y2Nlc3M6IHN0b2NrQ2hlY2suc3VjY2VzcyxcbiAgICAgICAgICBhdmFpbGFibGVTdG9jazogc3RvY2tDaGVjay5hdmFpbGFibGVTdG9jayxcbiAgICAgICAgICB0b3RhbFN0b2NrOiBzdG9ja0NoZWNrLnRvdGFsU3RvY2ssXG4gICAgICAgICAgcmVzZXJ2ZWRTdG9jazogc3RvY2tDaGVjay5yZXNlcnZlZFN0b2NrXG4gICAgICAgIH0pO1xuXG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgICAgIG1lc3NhZ2U6ICdTdG9jayBSZXNlcnZhdGlvbiBBUEknLFxuICAgICAgICAgIGVuZHBvaW50czoge1xuICAgICAgICAgICAgJ1BPU1QgL2FwaS9yZXNlcnZhdGlvbnMnOiB7XG4gICAgICAgICAgICAgIGFjdGlvbnM6IFsnY3JlYXRlJywgJ2NvbmZpcm0nLCAncmVsZWFzZScsICdjbGVhbnVwJ10sXG4gICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiAnTWFuYWdlIHN0b2NrIHJlc2VydmF0aW9ucydcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAnR0VUIC9hcGkvcmVzZXJ2YXRpb25zP2FjdGlvbj11c2VyX3Jlc2VydmF0aW9ucyZ1c2VySWQ9WCc6ICdHZXQgdXNlciByZXNlcnZhdGlvbnMnLFxuICAgICAgICAgICAgJ0dFVCAvYXBpL3Jlc2VydmF0aW9ucz9hY3Rpb249Y2hlY2tfc3RvY2smcHJvZHVjdElkPVgnOiAnQ2hlY2sgYXZhaWxhYmxlIHN0b2NrJ1xuICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgfVxuXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgaW4gcmVzZXJ2YXRpb25zIEdFVCBBUEk6JywgZXJyb3IpO1xuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgZXJyb3I6ICdJbnRlcm5hbCBzZXJ2ZXIgZXJyb3InIH0sXG4gICAgICB7IHN0YXR1czogNTAwIH1cbiAgICApO1xuICB9XG59XG5cbi8vIERFTEVURSAtIFJlbGVhc2Ugc3BlY2lmaWMgcmVzZXJ2YXRpb25cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBERUxFVEUocmVxdWVzdDogTmV4dFJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCB7IHNlYXJjaFBhcmFtcyB9ID0gbmV3IFVSTChyZXF1ZXN0LnVybCk7XG4gICAgY29uc3QgcmVzZXJ2YXRpb25JZCA9IHNlYXJjaFBhcmFtcy5nZXQoJ3Jlc2VydmF0aW9uSWQnKTtcblxuICAgIGlmICghcmVzZXJ2YXRpb25JZCkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IGVycm9yOiAnTWlzc2luZyByZXNlcnZhdGlvbklkIHBhcmFtZXRlcicgfSxcbiAgICAgICAgeyBzdGF0dXM6IDQwMCB9XG4gICAgICApO1xuICAgIH1cblxuICAgIGNvbnN0IHJlbGVhc2VkID0gYXdhaXQgcmVsZWFzZVJlc2VydmF0aW9uKHJlc2VydmF0aW9uSWQpO1xuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICBzdWNjZXNzOiByZWxlYXNlZCxcbiAgICAgIG1lc3NhZ2U6IHJlbGVhc2VkID8gJ1Jlc2VydmF0aW9uIHJlbGVhc2VkJyA6ICdGYWlsZWQgdG8gcmVsZWFzZSByZXNlcnZhdGlvbidcbiAgICB9KTtcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGluIHJlc2VydmF0aW9ucyBERUxFVEUgQVBJOicsIGVycm9yKTtcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICB7IGVycm9yOiAnSW50ZXJuYWwgc2VydmVyIGVycm9yJyB9LFxuICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgKTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsImNyZWF0ZVN0b2NrUmVzZXJ2YXRpb24iLCJjaGVja0F2YWlsYWJsZVN0b2NrIiwiZ2V0VXNlckFjdGl2ZVJlc2VydmF0aW9ucyIsImNvbmZpcm1SZXNlcnZhdGlvbiIsInJlbGVhc2VSZXNlcnZhdGlvbiIsImNsZWFudXBFeHBpcmVkUmVzZXJ2YXRpb25zIiwiUE9TVCIsInJlcXVlc3QiLCJib2R5IiwianNvbiIsInByb2R1Y3RJZCIsInF1YW50aXR5IiwidXNlcklkIiwidmFyaWF0aW9uSWQiLCJjYXJ0SWQiLCJhY3Rpb24iLCJlcnJvciIsInN0YXR1cyIsInJlc3VsdCIsInBhcnNlSW50Iiwic3VjY2VzcyIsInJlc2VydmF0aW9uIiwibWVzc2FnZSIsImF2YWlsYWJsZVN0b2NrIiwicmVzZXJ2YXRpb25JZCIsImNvbmZpcm1lZCIsInJlbGVhc2VkIiwiY2xlYW5lZFVwIiwiY29uc29sZSIsIkdFVCIsInNlYXJjaFBhcmFtcyIsIlVSTCIsInVybCIsImdldCIsInJlc2VydmF0aW9ucyIsImNvdW50IiwibGVuZ3RoIiwic3RvY2tDaGVjayIsInVuZGVmaW5lZCIsInRvdGFsU3RvY2siLCJyZXNlcnZlZFN0b2NrIiwiZW5kcG9pbnRzIiwiYWN0aW9ucyIsImRlc2NyaXB0aW9uIiwiREVMRVRFIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/reservations/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stockReservation.ts":
/*!*************************************!*\
  !*** ./src/lib/stockReservation.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkAvailableStock: () => (/* binding */ checkAvailableStock),\n/* harmony export */   cleanupExpiredReservations: () => (/* binding */ cleanupExpiredReservations),\n/* harmony export */   confirmReservation: () => (/* binding */ confirmReservation),\n/* harmony export */   createStockReservation: () => (/* binding */ createStockReservation),\n/* harmony export */   getUserActiveReservations: () => (/* binding */ getUserActiveReservations),\n/* harmony export */   releaseReservation: () => (/* binding */ releaseReservation)\n/* harmony export */ });\n/* harmony import */ var _upstash_redis__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @upstash/redis */ \"(rsc)/./node_modules/@upstash/redis/nodejs.mjs\");\n\n// Initialize Redis\nconst redis = process.env.UPSTASH_REDIS_REST_URL && process.env.UPSTASH_REDIS_REST_TOKEN ? new _upstash_redis__WEBPACK_IMPORTED_MODULE_0__.Redis({\n    url: process.env.UPSTASH_REDIS_REST_URL,\n    token: process.env.UPSTASH_REDIS_REST_TOKEN\n}) : null;\n// Reservation configuration\nconst RESERVATION_CONFIG = {\n    DURATION_MINUTES: 15,\n    CLEANUP_INTERVAL: 60,\n    MAX_RESERVATIONS_PER_USER: 10 // Prevent abuse\n};\n// Generate unique reservation ID\nfunction generateReservationId() {\n    return `res_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;\n}\n// Get reservation key for Redis\nfunction getReservationKey(productId, variationId) {\n    return variationId ? `reservation:product:${productId}:variation:${variationId}` : `reservation:product:${productId}`;\n}\n// Get user reservations key\nfunction getUserReservationsKey(userId) {\n    return `user_reservations:${userId}`;\n}\n// Create a stock reservation\nasync function createStockReservation(productId, quantity, userId, variationId, cartId) {\n    if (!redis) {\n        return {\n            success: false,\n            error: \"Reservation service unavailable\"\n        };\n    }\n    try {\n        // Check if user has too many active reservations\n        const userReservations = await getUserActiveReservations(userId);\n        if (userReservations.length >= RESERVATION_CONFIG.MAX_RESERVATIONS_PER_USER) {\n            return {\n                success: false,\n                error: \"Too many active reservations\"\n            };\n        }\n        // Get current stock and existing reservations\n        const stockCheck = await checkAvailableStock(productId, variationId);\n        if (!stockCheck.success || (stockCheck.availableStock || 0) < quantity) {\n            return {\n                success: false,\n                error: \"Insufficient stock available\",\n                availableStock: stockCheck.availableStock\n            };\n        }\n        // Create reservation\n        const now = new Date();\n        const expiresAt = new Date(now.getTime() + RESERVATION_CONFIG.DURATION_MINUTES * 60 * 1000);\n        const reservation = {\n            id: generateReservationId(),\n            productId,\n            variationId,\n            quantity,\n            userId,\n            reservedAt: now.toISOString(),\n            expiresAt: expiresAt.toISOString(),\n            status: \"active\",\n            cartId\n        };\n        // Store reservation in Redis with TTL\n        const reservationKey = `${getReservationKey(productId, variationId)}:${reservation.id}`;\n        await redis.set(reservationKey, reservation, {\n            ex: RESERVATION_CONFIG.DURATION_MINUTES * 60\n        });\n        // Add to user's reservations list\n        const userKey = getUserReservationsKey(userId);\n        await redis.sadd(userKey, reservation.id);\n        await redis.expire(userKey, RESERVATION_CONFIG.DURATION_MINUTES * 60);\n        console.log(`Created stock reservation: ${reservation.id} for product ${productId} (${quantity} items)`);\n        return {\n            success: true,\n            reservation\n        };\n    } catch (error) {\n        console.error(\"Error creating stock reservation:\", error);\n        return {\n            success: false,\n            error: \"Failed to create reservation\"\n        };\n    }\n}\n// Check available stock (total - confirmed sales - active reservations)\nasync function checkAvailableStock(productId, variationId) {\n    if (!redis) {\n        return {\n            success: false\n        };\n    }\n    try {\n        // Get actual stock from WooCommerce (you'd implement this)\n        const totalStock = await getTotalStockFromWooCommerce(productId, variationId);\n        if (totalStock === null) {\n            return {\n                success: false\n            };\n        }\n        // Get all active reservations for this product\n        const reservedStock = await getReservedStock(productId, variationId);\n        const availableStock = Math.max(0, totalStock - reservedStock);\n        return {\n            success: true,\n            availableStock,\n            totalStock,\n            reservedStock\n        };\n    } catch (error) {\n        console.error(\"Error checking available stock:\", error);\n        return {\n            success: false\n        };\n    }\n}\n// Get total reserved stock for a product\nasync function getReservedStock(productId, variationId) {\n    if (!redis) return 0;\n    try {\n        const pattern = `${getReservationKey(productId, variationId)}:*`;\n        const keys = await redis.keys(pattern);\n        let totalReserved = 0;\n        for (const key of keys){\n            const reservation = await redis.get(key);\n            if (reservation && reservation.status === \"active\") {\n                totalReserved += reservation.quantity;\n            }\n        }\n        return totalReserved;\n    } catch (error) {\n        console.error(\"Error getting reserved stock:\", error);\n        return 0;\n    }\n}\n// Get user's active reservations\nasync function getUserActiveReservations(userId) {\n    if (!redis) return [];\n    try {\n        const userKey = getUserReservationsKey(userId);\n        const reservationIds = await redis.smembers(userKey);\n        const reservations = [];\n        for (const id of reservationIds){\n            // Find the reservation by scanning all product keys\n            const keys = await redis.keys(`reservation:product:*:${id}`);\n            for (const key of keys){\n                const reservation = await redis.get(key);\n                if (reservation && reservation.status === \"active\") {\n                    reservations.push(reservation);\n                }\n            }\n        }\n        return reservations;\n    } catch (error) {\n        console.error(\"Error getting user reservations:\", error);\n        return [];\n    }\n}\n// Confirm reservation (convert to sale)\nasync function confirmReservation(reservationId) {\n    if (!redis) return false;\n    try {\n        // Find and update reservation\n        const keys = await redis.keys(`reservation:product:*:${reservationId}`);\n        for (const key of keys){\n            const reservation = await redis.get(key);\n            if (reservation) {\n                reservation.status = \"confirmed\";\n                await redis.set(key, reservation, {\n                    ex: 86400\n                }); // Keep for 24 hours for records\n                console.log(`Confirmed reservation: ${reservationId}`);\n                return true;\n            }\n        }\n        return false;\n    } catch (error) {\n        console.error(\"Error confirming reservation:\", error);\n        return false;\n    }\n}\n// Release reservation (cancel or expire)\nasync function releaseReservation(reservationId) {\n    if (!redis) return false;\n    try {\n        // Find and remove reservation\n        const keys = await redis.keys(`reservation:product:*:${reservationId}`);\n        for (const key of keys){\n            await redis.del(key);\n            console.log(`Released reservation: ${reservationId}`);\n        }\n        return true;\n    } catch (error) {\n        console.error(\"Error releasing reservation:\", error);\n        return false;\n    }\n}\n// Placeholder for getting actual stock from WooCommerce\nasync function getTotalStockFromWooCommerce(productId, variationId) {\n    // This would make an API call to your stock endpoint\n    try {\n        const baseUrl = \"https://ankkorwoo.vercel.app\" || 0;\n        const url = `${baseUrl}/api/products/${productId}/stock${variationId ? `?variation_id=${variationId}` : \"\"}`;\n        const response = await fetch(url);\n        if (!response.ok) return null;\n        const data = await response.json();\n        return data.stockQuantity || 0;\n    } catch (error) {\n        console.error(\"Error fetching stock from WooCommerce:\", error);\n        return null;\n    }\n}\n// Clean up expired reservations (run periodically)\nasync function cleanupExpiredReservations() {\n    if (!redis) return 0;\n    try {\n        const keys = await redis.keys(\"reservation:product:*\");\n        let cleanedUp = 0;\n        for (const key of keys){\n            const reservation = await redis.get(key);\n            if (reservation && new Date(reservation.expiresAt) < new Date()) {\n                await redis.del(key);\n                cleanedUp++;\n            }\n        }\n        console.log(`Cleaned up ${cleanedUp} expired reservations`);\n        return cleanedUp;\n    } catch (error) {\n        console.error(\"Error cleaning up expired reservations:\", error);\n        return 0;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stockReservation.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@upstash","vendor-chunks/crypto-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Freservations%2Froute&page=%2Fapi%2Freservations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Freservations%2Froute.ts&appDir=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cankkorwoo%5Cankkor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();