"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("commons-node_modules_framer-motion_dist_es_a",{

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs":
/*!************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs ***!
  \************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PopChild: function() { return /* binding */ PopChild; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n\n\n/**\n * Measurement functionality has to be within a separate component\n * to leverage snapshot lifecycle.\n */\nclass PopChildMeasure extends react__WEBPACK_IMPORTED_MODULE_0__.Component {\n    getSnapshotBeforeUpdate(prevProps) {\n        const element = this.props.childRef.current;\n        if (element && prevProps.isPresent && !this.props.isPresent) {\n            const size = this.props.sizeRef.current;\n            size.height = element.offsetHeight || 0;\n            size.width = element.offsetWidth || 0;\n            size.top = element.offsetTop;\n            size.left = element.offsetLeft;\n        }\n        return null;\n    }\n    /**\n     * Required with getSnapshotBeforeUpdate to stop React complaining.\n     */\n    componentDidUpdate() { }\n    render() {\n        return this.props.children;\n    }\n}\nfunction PopChild({ children, isPresent }) {\n    const id = (0,react__WEBPACK_IMPORTED_MODULE_0__.useId)();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const size = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0,\n    });\n    /**\n     * We create and inject a style block so we can apply this explicit\n     * sizing in a non-destructive manner by just deleting the style block.\n     *\n     * We can't apply size via render as the measurement happens\n     * in getSnapshotBeforeUpdate (post-render), likewise if we apply the\n     * styles directly on the DOM node, we might be overwriting\n     * styles set via the style prop.\n     */\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useInsertionEffect)(() => {\n        const { width, height, top, left } = size.current;\n        if (isPresent || !ref.current || !width || !height)\n            return;\n        ref.current.dataset.motionPopId = id;\n        const style = document.createElement(\"style\");\n        document.head.appendChild(style);\n        if (style.sheet) {\n            style.sheet.insertRule(`\n          [data-motion-pop-id=\"${id}\"] {\n            position: absolute !important;\n            width: ${width}px !important;\n            height: ${height}px !important;\n            top: ${top}px !important;\n            left: ${left}px !important;\n          }\n        `);\n        }\n        return () => {\n            document.head.removeChild(style);\n        };\n    }, [isPresent]);\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(PopChildMeasure, { isPresent: isPresent, childRef: ref, sizeRef: size }, react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(children, { ref })));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs":
/*!*****************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs ***!
  \*****************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PresenceChild: function() { return /* binding */ PresenceChild; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _context_PresenceContext_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../context/PresenceContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/PresenceContext.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n/* harmony import */ var _PopChild_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./PopChild.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs\");\n\n\n\n\n\n\nconst PresenceChild = ({ children, initial, isPresent, onExitComplete, custom, presenceAffectsLayout, mode, }) => {\n    const presenceChildren = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_1__.useConstant)(newChildrenMap);\n    const id = (0,react__WEBPACK_IMPORTED_MODULE_0__.useId)();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n        id,\n        initial,\n        isPresent,\n        custom,\n        onExitComplete: (childId) => {\n            presenceChildren.set(childId, true);\n            for (const isComplete of presenceChildren.values()) {\n                if (!isComplete)\n                    return; // can stop searching when any is incomplete\n            }\n            onExitComplete && onExitComplete();\n        },\n        register: (childId) => {\n            presenceChildren.set(childId, false);\n            return () => presenceChildren.delete(childId);\n        },\n    }), \n    /**\n     * If the presence of a child affects the layout of the components around it,\n     * we want to make a new context value to ensure they get re-rendered\n     * so they can detect that layout change.\n     */\n    presenceAffectsLayout ? undefined : [isPresent]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n        presenceChildren.forEach((_, key) => presenceChildren.set(key, false));\n    }, [isPresent]);\n    /**\n     * If there's no `motion` components to fire exit animations, we want to remove this\n     * component immediately.\n     */\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        !isPresent &&\n            !presenceChildren.size &&\n            onExitComplete &&\n            onExitComplete();\n    }, [isPresent]);\n    if (mode === \"popLayout\") {\n        children = react__WEBPACK_IMPORTED_MODULE_0__.createElement(_PopChild_mjs__WEBPACK_IMPORTED_MODULE_2__.PopChild, { isPresent: isPresent }, children);\n    }\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_context_PresenceContext_mjs__WEBPACK_IMPORTED_MODULE_3__.PresenceContext.Provider, { value: context }, children));\n};\nfunction newChildrenMap() {\n    return new Map();\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnimatePresence: function() { return /* binding */ AnimatePresence; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _utils_use_force_update_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../utils/use-force-update.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-force-update.mjs\");\n/* harmony import */ var _utils_use_is_mounted_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/use-is-mounted.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-is-mounted.mjs\");\n/* harmony import */ var _PresenceChild_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./PresenceChild.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs\");\n/* harmony import */ var _context_LayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../context/LayoutGroupContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/LayoutGroupContext.mjs\");\n/* harmony import */ var _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../utils/use-isomorphic-effect.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs\");\n/* harmony import */ var _utils_use_unmount_effect_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../utils/use-unmount-effect.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-unmount-effect.mjs\");\n/* harmony import */ var _utils_errors_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../utils/errors.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/errors.mjs\");\n\n\n\n\n\n\n\n\n\n\nconst getChildKey = (child) => child.key || \"\";\nfunction updateChildLookup(children, allChildren) {\n    children.forEach((child) => {\n        const key = getChildKey(child);\n        allChildren.set(key, child);\n    });\n}\nfunction onlyElements(children) {\n    const filtered = [];\n    // We use forEach here instead of map as map mutates the component key by preprending `.$`\n    react__WEBPACK_IMPORTED_MODULE_0__.Children.forEach(children, (child) => {\n        if ((0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(child))\n            filtered.push(child);\n    });\n    return filtered;\n}\n/**\n * `AnimatePresence` enables the animation of components that have been removed from the tree.\n *\n * When adding/removing more than a single child, every child **must** be given a unique `key` prop.\n *\n * Any `motion` components that have an `exit` property defined will animate out when removed from\n * the tree.\n *\n * ```jsx\n * import { motion, AnimatePresence } from 'framer-motion'\n *\n * export const Items = ({ items }) => (\n *   <AnimatePresence>\n *     {items.map(item => (\n *       <motion.div\n *         key={item.id}\n *         initial={{ opacity: 0 }}\n *         animate={{ opacity: 1 }}\n *         exit={{ opacity: 0 }}\n *       />\n *     ))}\n *   </AnimatePresence>\n * )\n * ```\n *\n * You can sequence exit animations throughout a tree using variants.\n *\n * If a child contains multiple `motion` components with `exit` props, it will only unmount the child\n * once all `motion` components have finished animating out. Likewise, any components using\n * `usePresence` all need to call `safeToRemove`.\n *\n * @public\n */\nconst AnimatePresence = ({ children, custom, initial = true, onExitComplete, exitBeforeEnter, presenceAffectsLayout = true, mode = \"sync\", }) => {\n    (0,_utils_errors_mjs__WEBPACK_IMPORTED_MODULE_1__.invariant)(!exitBeforeEnter, \"Replace exitBeforeEnter with mode='wait'\");\n    // We want to force a re-render once all exiting animations have finished. We\n    // either use a local forceRender function, or one from a parent context if it exists.\n    const forceRender = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_LayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_2__.LayoutGroupContext).forceRender || (0,_utils_use_force_update_mjs__WEBPACK_IMPORTED_MODULE_3__.useForceUpdate)()[0];\n    const isMounted = (0,_utils_use_is_mounted_mjs__WEBPACK_IMPORTED_MODULE_4__.useIsMounted)();\n    // Filter out any children that aren't ReactElements. We can only track ReactElements with a props.key\n    const filteredChildren = onlyElements(children);\n    let childrenToRender = filteredChildren;\n    const exitingChildren = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(new Map()).current;\n    // Keep a living record of the children we're actually rendering so we\n    // can diff to figure out which are entering and exiting\n    const presentChildren = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(childrenToRender);\n    // A lookup table to quickly reference components by key\n    const allChildren = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(new Map()).current;\n    // If this is the initial component render, just deal with logic surrounding whether\n    // we play onMount animations or not.\n    const isInitialRender = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(true);\n    (0,_utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_5__.useIsomorphicLayoutEffect)(() => {\n        isInitialRender.current = false;\n        updateChildLookup(filteredChildren, allChildren);\n        presentChildren.current = childrenToRender;\n    });\n    (0,_utils_use_unmount_effect_mjs__WEBPACK_IMPORTED_MODULE_6__.useUnmountEffect)(() => {\n        isInitialRender.current = true;\n        allChildren.clear();\n        exitingChildren.clear();\n    });\n    if (isInitialRender.current) {\n        return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, childrenToRender.map((child) => (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_PresenceChild_mjs__WEBPACK_IMPORTED_MODULE_7__.PresenceChild, { key: getChildKey(child), isPresent: true, initial: initial ? undefined : false, presenceAffectsLayout: presenceAffectsLayout, mode: mode }, child)))));\n    }\n    // If this is a subsequent render, deal with entering and exiting children\n    childrenToRender = [...childrenToRender];\n    // Diff the keys of the currently-present and target children to update our\n    // exiting list.\n    const presentKeys = presentChildren.current.map(getChildKey);\n    const targetKeys = filteredChildren.map(getChildKey);\n    // Diff the present children with our target children and mark those that are exiting\n    const numPresent = presentKeys.length;\n    for (let i = 0; i < numPresent; i++) {\n        const key = presentKeys[i];\n        if (targetKeys.indexOf(key) === -1 && !exitingChildren.has(key)) {\n            exitingChildren.set(key, undefined);\n        }\n    }\n    // If we currently have exiting children, and we're deferring rendering incoming children\n    // until after all current children have exiting, empty the childrenToRender array\n    if (mode === \"wait\" && exitingChildren.size) {\n        childrenToRender = [];\n    }\n    // Loop through all currently exiting components and clone them to overwrite `animate`\n    // with any `exit` prop they might have defined.\n    exitingChildren.forEach((component, key) => {\n        // If this component is actually entering again, early return\n        if (targetKeys.indexOf(key) !== -1)\n            return;\n        const child = allChildren.get(key);\n        if (!child)\n            return;\n        const insertionIndex = presentKeys.indexOf(key);\n        let exitingComponent = component;\n        if (!exitingComponent) {\n            const onExit = () => {\n                // clean up the exiting children map\n                exitingChildren.delete(key);\n                // compute the keys of children that were rendered once but are no longer present\n                // this could happen in case of too many fast consequent renderings\n                // @link https://github.com/framer/motion/issues/2023\n                const leftOverKeys = Array.from(allChildren.keys()).filter((childKey) => !targetKeys.includes(childKey));\n                // clean up the all children map\n                leftOverKeys.forEach((leftOverKey) => allChildren.delete(leftOverKey));\n                // make sure to render only the children that are actually visible\n                presentChildren.current = filteredChildren.filter((presentChild) => {\n                    const presentChildKey = getChildKey(presentChild);\n                    return (\n                    // filter out the node exiting\n                    presentChildKey === key ||\n                        // filter out the leftover children\n                        leftOverKeys.includes(presentChildKey));\n                });\n                // Defer re-rendering until all exiting children have indeed left\n                if (!exitingChildren.size) {\n                    if (isMounted.current === false)\n                        return;\n                    forceRender();\n                    onExitComplete && onExitComplete();\n                }\n            };\n            exitingComponent = (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_PresenceChild_mjs__WEBPACK_IMPORTED_MODULE_7__.PresenceChild, { key: getChildKey(child), isPresent: false, onExitComplete: onExit, custom: custom, presenceAffectsLayout: presenceAffectsLayout, mode: mode }, child));\n            exitingChildren.set(key, exitingComponent);\n        }\n        childrenToRender.splice(insertionIndex, 0, exitingComponent);\n    });\n    // Add `MotionContext` even to children that don't need it to ensure we're rendering\n    // the same tree between renders\n    childrenToRender = childrenToRender.map((child) => {\n        const key = child.key;\n        return exitingChildren.has(key) ? (child) : (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_PresenceChild_mjs__WEBPACK_IMPORTED_MODULE_7__.PresenceChild, { key: getChildKey(child), isPresent: true, presenceAffectsLayout: presenceAffectsLayout, mode: mode }, child));\n    });\n    if ( true &&\n        mode === \"wait\" &&\n        childrenToRender.length > 1) {\n        console.warn(`You're attempting to animate multiple children within AnimatePresence, but its mode is set to \"wait\". This will lead to odd visual behaviour.`);\n    }\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, exitingChildren.size\n        ? childrenToRender\n        : childrenToRender.map((child) => (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(child))));\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\n"));

/***/ })

});