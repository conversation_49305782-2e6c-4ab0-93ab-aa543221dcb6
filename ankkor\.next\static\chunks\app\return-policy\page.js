/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/return-policy/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cankkorwoo%5C%5Cankkor%5C%5Csrc%5C%5Capp%5C%5Creturn-policy%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!***************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cankkorwoo%5C%5Cankkor%5C%5Csrc%5C%5Capp%5C%5Creturn-policy%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \***************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/return-policy/page.tsx */ \"(app-pages-browser)/./src/app/return-policy/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q2Fua2tvcndvbyU1QyU1Q2Fua2tvciU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3JldHVybi1wb2xpY3klNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLDBMQUE2RiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvPzJkZGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFxhbmtrb3J3b29cXFxcYW5ra29yXFxcXHNyY1xcXFxhcHBcXFxccmV0dXJuLXBvbGljeVxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cankkorwoo%5C%5Cankkor%5C%5Csrc%5C%5Capp%5C%5Creturn-policy%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js":
/*!*************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/package.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Package; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */ \nconst Package = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Package\", [\n    [\n        \"path\",\n        {\n            d: \"m7.5 4.27 9 5.15\",\n            key: \"1c824w\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z\",\n            key: \"hh9hay\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m3.3 7 8.7 5 8.7-5\",\n            key: \"g66t2b\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 22V12\",\n            key: \"d0xqtd\"\n        }\n    ]\n]);\n //# sourceMappingURL=package.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/return-policy/page.tsx":
/*!****************************************!*\
  !*** ./src/app/return-policy/page.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ReturnPolicyPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Package_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Package,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Package_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Package,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Package_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Package,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction ReturnPolicyPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-[#f8f8f5] min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative bg-[#2c2c27] h-[30vh] flex items-center justify-center overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[#2c2c27]/90\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[url('https://images.unsplash.com/photo-1512909006721-3d6018887383?q=80&w=2670&auto=format&fit=crop')] bg-cover bg-center opacity-20\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8,\n                            ease: [\n                                0.22,\n                                1,\n                                0.36,\n                                1\n                            ]\n                        },\n                        className: \"container mx-auto px-4 text-center relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"font-serif text-4xl md:text-5xl text-[#f8f8f5] mb-4\",\n                                children: \"Return Policy\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-[1px] w-24 bg-[#8a8778] mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-[#e5e2d9] max-w-2xl mx-auto\",\n                                children: \"Our commitment to your complete satisfaction\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-16 md:py-24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.2\n                    },\n                    className: \"max-w-4xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"prose prose-lg max-w-none text-[#5c5c52]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-12 p-8 bg-[#f4f3f0] border-l-4 border-[#8a8778]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"italic text-[#2c2c27]\",\n                                    children: \"At ANKKOR, customer satisfaction is our priority. If your order doesn't meet expectations, we offer a 7-day exchange policy to ensure you're completely happy with your purchase.\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center my-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-20 h-20 bg-[#f4f3f0] rounded-full flex items-center justify-center shadow-sm border border-[#e5e2d9]\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Package_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-8 h-8 text-[#8a8778]\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-serif text-2xl text-[#2c2c27] mt-12 mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"h-px w-10 bg-[#8a8778] mr-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Exchange Eligibility\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mb-6\",\n                                children: \"To qualify for an exchange, items must be unused, unwashed, and returned in original packaging with all tags intact. You must initiate the exchange within 7 days of receiving the product. Items that are worn, damaged, or missing tags will not be accepted.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-8 my-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-[#f4f3f0] p-6 border border-[#e5e2d9]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Package_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-5 h-5 text-[#8a8778] mr-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                                        lineNumber: 59,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-serif text-lg text-[#2c2c27]\",\n                                                        children: \"Exchange Options\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                                        lineNumber: 60,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm mb-3\",\n                                                children: \"We currently do not offer refunds. Instead, customers can:\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                                lineNumber: 62,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"mb-2\",\n                                                        children: \"Exchange the product for a different size or item\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                                        lineNumber: 64,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Receive ANKKOR Points of equal value, which can be redeemed on future purchases via our website\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                                        lineNumber: 65,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                                lineNumber: 63,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-[#f4f3f0] p-6 border border-[#e5e2d9]\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Package_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-5 h-5 text-[#8a8778] mr-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                                        lineNumber: 71,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-serif text-lg text-[#2c2c27]\",\n                                                        children: \"Non-Exchangeable Items\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                                        lineNumber: 72,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm mb-3\",\n                                                children: \"Certain products may be non-returnable, including:\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc pl-6 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"mb-2\",\n                                                        children: \"Accessories\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                                        lineNumber: 76,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"mb-2\",\n                                                        children: \"Discounted items\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                                        lineNumber: 77,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"Custom-made pieces (if applicable)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                                        lineNumber: 78,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm mt-3\",\n                                                children: \"These exceptions will be clearly stated on their respective product pages.\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-serif text-2xl text-[#2c2c27] mt-12 mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"h-px w-10 bg-[#8a8778] mr-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Exchange Process\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mb-6\",\n                                children: \"To start an exchange, simply contact us via WhatsApp, Instagram, or our website portal. Our team will guide you through the return process.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-serif text-2xl text-[#2c2c27] mt-12 mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"h-px w-10 bg-[#8a8778] mr-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"ANKKOR Points\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mb-6\",\n                                children: \"If you choose to receive ANKKOR Points instead of a direct exchange, these points will be credited to your account once we receive and inspect your returned item. Points have the same value as Indian Rupees and can be used for future purchases on our website.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-[#f4f3f0] p-8 border-l-4 border-[#8a8778] my-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-serif text-xl text-[#2c2c27] mb-3\",\n                                        children: \"Quality Assurance\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mb-0\",\n                                        children: \"Each ANKKOR product undergoes rigorous quality checks before dispatch. If you receive a product with manufacturing defects, please contact us immediately with photos of the issue, and we'll prioritize your exchange.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row md:justify-between items-center mt-16 pt-8 border-t border-[#e5e2d9]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-[#8a8778] mb-4 md:mb-0\",\n                                        children: \"Last Updated: April 2025\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/terms-of-service\",\n                                                className: \"text-sm text-[#2c2c27] hover:text-[#8a8778] transition-colors\",\n                                                children: \"Terms of Service\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/privacy-policy\",\n                                                className: \"text-sm text-[#2c2c27] hover:text-[#8a8778] transition-colors\",\n                                                children: \"Privacy Policy\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/shipping-policy\",\n                                                className: \"text-sm text-[#2c2c27] hover:text-[#8a8778] transition-colors\",\n                                                children: \"Shipping Policy\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\return-policy\\\\page.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n_c = ReturnPolicyPage;\nvar _c;\n$RefreshReg$(_c, \"ReturnPolicyPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcmV0dXJuLXBvbGljeS9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBRTBCO0FBQ2E7QUFDVjtBQUNrQztBQUVoRCxTQUFTTTtJQUN0QixxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7Ozs7OztrQ0FDZiw4REFBQ0Q7d0JBQUlDLFdBQVU7Ozs7OztrQ0FDZiw4REFBQ1AsaURBQU1BLENBQUNNLEdBQUc7d0JBQ1RFLFNBQVM7NEJBQUVDLFNBQVM7NEJBQUdDLEdBQUc7d0JBQUc7d0JBQzdCQyxTQUFTOzRCQUFFRixTQUFTOzRCQUFHQyxHQUFHO3dCQUFFO3dCQUM1QkUsWUFBWTs0QkFBRUMsVUFBVTs0QkFBS0MsTUFBTTtnQ0FBQztnQ0FBTTtnQ0FBRztnQ0FBTTs2QkFBRTt3QkFBQzt3QkFDdERQLFdBQVU7OzBDQUVWLDhEQUFDUTtnQ0FBR1IsV0FBVTswQ0FBc0Q7Ozs7OzswQ0FDcEUsOERBQUNEO2dDQUFJQyxXQUFVOzs7Ozs7MENBQ2YsOERBQUNTO2dDQUFFVCxXQUFVOzBDQUFtQzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQUtwRCw4REFBQ0Q7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNQLGlEQUFNQSxDQUFDTSxHQUFHO29CQUNURSxTQUFTO3dCQUFFQyxTQUFTO3dCQUFHQyxHQUFHO29CQUFHO29CQUM3QkMsU0FBUzt3QkFBRUYsU0FBUzt3QkFBR0MsR0FBRztvQkFBRTtvQkFDNUJFLFlBQVk7d0JBQUVDLFVBQVU7d0JBQUtJLE9BQU87b0JBQUk7b0JBQ3hDVixXQUFVOzhCQUVWLDRFQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDUztvQ0FBRVQsV0FBVTs4Q0FBd0I7Ozs7Ozs7Ozs7OzBDQUt2Qyw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDTCx5R0FBU0E7d0NBQUNLLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7MENBSXpCLDhEQUFDVztnQ0FBR1gsV0FBVTs7a0RBQ1osOERBQUNZO3dDQUFLWixXQUFVOzs7Ozs7b0NBQXFDOzs7Ozs7OzBDQUd2RCw4REFBQ1M7Z0NBQUVULFdBQVU7MENBQU87Ozs7OzswQ0FJcEIsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNKLHlHQUFPQTt3REFBQ0ksV0FBVTs7Ozs7O2tFQUNuQiw4REFBQ2E7d0RBQUdiLFdBQVU7a0VBQW9DOzs7Ozs7Ozs7Ozs7MERBRXBELDhEQUFDUztnREFBRVQsV0FBVTswREFBZTs7Ozs7OzBEQUM1Qiw4REFBQ2M7Z0RBQUdkLFdBQVU7O2tFQUNaLDhEQUFDZTt3REFBR2YsV0FBVTtrRUFBTzs7Ozs7O2tFQUNyQiw4REFBQ2U7a0VBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFJUiw4REFBQ2hCO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDSCx5R0FBV0E7d0RBQUNHLFdBQVU7Ozs7OztrRUFDdkIsOERBQUNhO3dEQUFHYixXQUFVO2tFQUFvQzs7Ozs7Ozs7Ozs7OzBEQUVwRCw4REFBQ1M7Z0RBQUVULFdBQVU7MERBQWU7Ozs7OzswREFDNUIsOERBQUNjO2dEQUFHZCxXQUFVOztrRUFDWiw4REFBQ2U7d0RBQUdmLFdBQVU7a0VBQU87Ozs7OztrRUFDckIsOERBQUNlO3dEQUFHZixXQUFVO2tFQUFPOzs7Ozs7a0VBQ3JCLDhEQUFDZTtrRUFBRzs7Ozs7Ozs7Ozs7OzBEQUVOLDhEQUFDTjtnREFBRVQsV0FBVTswREFBZTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUloQyw4REFBQ1c7Z0NBQUdYLFdBQVU7O2tEQUNaLDhEQUFDWTt3Q0FBS1osV0FBVTs7Ozs7O29DQUFxQzs7Ozs7OzswQ0FHdkQsOERBQUNTO2dDQUFFVCxXQUFVOzBDQUFPOzs7Ozs7MENBSXBCLDhEQUFDVztnQ0FBR1gsV0FBVTs7a0RBQ1osOERBQUNZO3dDQUFLWixXQUFVOzs7Ozs7b0NBQXFDOzs7Ozs7OzBDQUd2RCw4REFBQ1M7Z0NBQUVULFdBQVU7MENBQU87Ozs7OzswQ0FJcEIsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ2E7d0NBQUdiLFdBQVU7a0RBQXlDOzs7Ozs7a0RBQ3ZELDhEQUFDUzt3Q0FBRVQsV0FBVTtrREFBTzs7Ozs7Ozs7Ozs7OzBDQUt0Qiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDUzt3Q0FBRVQsV0FBVTtrREFBc0M7Ozs7OztrREFDbkQsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ04saURBQUlBO2dEQUFDc0IsTUFBSztnREFBb0JoQixXQUFVOzBEQUFnRTs7Ozs7OzBEQUd6Ryw4REFBQ04saURBQUlBO2dEQUFDc0IsTUFBSztnREFBa0JoQixXQUFVOzBEQUFnRTs7Ozs7OzBEQUd2Ryw4REFBQ04saURBQUlBO2dEQUFDc0IsTUFBSztnREFBbUJoQixXQUFVOzBEQUFnRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVV4SDtLQXRId0JGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvcmV0dXJuLXBvbGljeS9wYWdlLnRzeD82MTNjIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcclxuXHJcbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB7IG1vdGlvbiB9IGZyb20gJ2ZyYW1lci1tb3Rpb24nO1xyXG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnO1xyXG5pbXBvcnQgeyBSZWZyZXNoQ3csIFBhY2thZ2UsIEFsZXJ0Q2lyY2xlIH0gZnJvbSAnbHVjaWRlLXJlYWN0JztcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJldHVyblBvbGljeVBhZ2UoKSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctWyNmOGY4ZjVdIG1pbi1oLXNjcmVlblwiPlxyXG4gICAgICB7LyogSGVybyBTZWN0aW9uICovfVxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGJnLVsjMmMyYzI3XSBoLVszMHZoXSBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBvdmVyZmxvdy1oaWRkZW5cIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctWyMyYzJjMjddLzkwXCI+PC9kaXY+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGJnLVt1cmwoJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTEyOTA5MDA2NzIxLTNkNjAxODg4NzM4Mz9xPTgwJnc9MjY3MCZhdXRvPWZvcm1hdCZmaXQ9Y3JvcCcpXSBiZy1jb3ZlciBiZy1jZW50ZXIgb3BhY2l0eS0yMFwiPjwvZGl2PlxyXG4gICAgICAgIDxtb3Rpb24uZGl2IFxyXG4gICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAyMCB9fVxyXG4gICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19XHJcbiAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjgsIGVhc2U6IFswLjIyLCAxLCAwLjM2LCAxXSB9fVxyXG4gICAgICAgICAgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHgtNCB0ZXh0LWNlbnRlciByZWxhdGl2ZSB6LTEwXCJcclxuICAgICAgICA+XHJcbiAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwiZm9udC1zZXJpZiB0ZXh0LTR4bCBtZDp0ZXh0LTV4bCB0ZXh0LVsjZjhmOGY1XSBtYi00XCI+UmV0dXJuIFBvbGljeTwvaDE+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtWzFweF0gdy0yNCBiZy1bIzhhODc3OF0gbXgtYXV0byBtYi00XCI+PC9kaXY+XHJcbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LVsjZTVlMmQ5XSBtYXgtdy0yeGwgbXgtYXV0b1wiPk91ciBjb21taXRtZW50IHRvIHlvdXIgY29tcGxldGUgc2F0aXNmYWN0aW9uPC9wPlxyXG4gICAgICAgIDwvbW90aW9uLmRpdj5cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICB7LyogTWFpbiBDb250ZW50ICovfVxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHB4LTQgcHktMTYgbWQ6cHktMjRcIj5cclxuICAgICAgICA8bW90aW9uLmRpdlxyXG4gICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAzMCB9fVxyXG4gICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19XHJcbiAgICAgICAgICB0cmFuc2l0aW9uPXt7IGR1cmF0aW9uOiAwLjgsIGRlbGF5OiAwLjIgfX1cclxuICAgICAgICAgIGNsYXNzTmFtZT1cIm1heC13LTR4bCBteC1hdXRvXCJcclxuICAgICAgICA+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInByb3NlIHByb3NlLWxnIG1heC13LW5vbmUgdGV4dC1bIzVjNWM1Ml1cIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi0xMiBwLTggYmctWyNmNGYzZjBdIGJvcmRlci1sLTQgYm9yZGVyLVsjOGE4Nzc4XVwiPlxyXG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIml0YWxpYyB0ZXh0LVsjMmMyYzI3XVwiPlxyXG4gICAgICAgICAgICAgICAgQXQgQU5LS09SLCBjdXN0b21lciBzYXRpc2ZhY3Rpb24gaXMgb3VyIHByaW9yaXR5LiBJZiB5b3VyIG9yZGVyIGRvZXNuJ3QgbWVldCBleHBlY3RhdGlvbnMsIHdlIG9mZmVyIGEgNy1kYXkgZXhjaGFuZ2UgcG9saWN5IHRvIGVuc3VyZSB5b3UncmUgY29tcGxldGVseSBoYXBweSB3aXRoIHlvdXIgcHVyY2hhc2UuXHJcbiAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgXHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlciBteS0xMlwiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yMCBoLTIwIGJnLVsjZjRmM2YwXSByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgc2hhZG93LXNtIGJvcmRlciBib3JkZXItWyNlNWUyZDldXCI+XHJcbiAgICAgICAgICAgICAgICA8UmVmcmVzaEN3IGNsYXNzTmFtZT1cInctOCBoLTggdGV4dC1bIzhhODc3OF1cIiAvPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgXHJcbiAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJmb250LXNlcmlmIHRleHQtMnhsIHRleHQtWyMyYzJjMjddIG10LTEyIG1iLTQgZmxleCBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJoLXB4IHctMTAgYmctWyM4YTg3NzhdIG1yLTRcIj48L3NwYW4+XHJcbiAgICAgICAgICAgICAgRXhjaGFuZ2UgRWxpZ2liaWxpdHlcclxuICAgICAgICAgICAgPC9oMj5cclxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwibWItNlwiPlxyXG4gICAgICAgICAgICAgIFRvIHF1YWxpZnkgZm9yIGFuIGV4Y2hhbmdlLCBpdGVtcyBtdXN0IGJlIHVudXNlZCwgdW53YXNoZWQsIGFuZCByZXR1cm5lZCBpbiBvcmlnaW5hbCBwYWNrYWdpbmcgd2l0aCBhbGwgdGFncyBpbnRhY3QuIFlvdSBtdXN0IGluaXRpYXRlIHRoZSBleGNoYW5nZSB3aXRoaW4gNyBkYXlzIG9mIHJlY2VpdmluZyB0aGUgcHJvZHVjdC4gSXRlbXMgdGhhdCBhcmUgd29ybiwgZGFtYWdlZCwgb3IgbWlzc2luZyB0YWdzIHdpbGwgbm90IGJlIGFjY2VwdGVkLlxyXG4gICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgIFxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTggbXktMTJcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLVsjZjRmM2YwXSBwLTYgYm9yZGVyIGJvcmRlci1bI2U1ZTJkOV1cIj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgbWItNFwiPlxyXG4gICAgICAgICAgICAgICAgICA8UGFja2FnZSBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtWyM4YTg3NzhdIG1yLTNcIiAvPlxyXG4gICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1zZXJpZiB0ZXh0LWxnIHRleHQtWyMyYzJjMjddXCI+RXhjaGFuZ2UgT3B0aW9uczwvaDM+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gbWItM1wiPldlIGN1cnJlbnRseSBkbyBub3Qgb2ZmZXIgcmVmdW5kcy4gSW5zdGVhZCwgY3VzdG9tZXJzIGNhbjo8L3A+XHJcbiAgICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwibGlzdC1kaXNjIHBsLTYgdGV4dC1zbVwiPlxyXG4gICAgICAgICAgICAgICAgICA8bGkgY2xhc3NOYW1lPVwibWItMlwiPkV4Y2hhbmdlIHRoZSBwcm9kdWN0IGZvciBhIGRpZmZlcmVudCBzaXplIG9yIGl0ZW08L2xpPlxyXG4gICAgICAgICAgICAgICAgICA8bGk+UmVjZWl2ZSBBTktLT1IgUG9pbnRzIG9mIGVxdWFsIHZhbHVlLCB3aGljaCBjYW4gYmUgcmVkZWVtZWQgb24gZnV0dXJlIHB1cmNoYXNlcyB2aWEgb3VyIHdlYnNpdGU8L2xpPlxyXG4gICAgICAgICAgICAgICAgPC91bD5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLVsjZjRmM2YwXSBwLTYgYm9yZGVyIGJvcmRlci1bI2U1ZTJkOV1cIj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgbWItNFwiPlxyXG4gICAgICAgICAgICAgICAgICA8QWxlcnRDaXJjbGUgY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LVsjOGE4Nzc4XSBtci0zXCIgLz5cclxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZvbnQtc2VyaWYgdGV4dC1sZyB0ZXh0LVsjMmMyYzI3XVwiPk5vbi1FeGNoYW5nZWFibGUgSXRlbXM8L2gzPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIG1iLTNcIj5DZXJ0YWluIHByb2R1Y3RzIG1heSBiZSBub24tcmV0dXJuYWJsZSwgaW5jbHVkaW5nOjwvcD5cclxuICAgICAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJsaXN0LWRpc2MgcGwtNiB0ZXh0LXNtXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxsaSBjbGFzc05hbWU9XCJtYi0yXCI+QWNjZXNzb3JpZXM8L2xpPlxyXG4gICAgICAgICAgICAgICAgICA8bGkgY2xhc3NOYW1lPVwibWItMlwiPkRpc2NvdW50ZWQgaXRlbXM8L2xpPlxyXG4gICAgICAgICAgICAgICAgICA8bGk+Q3VzdG9tLW1hZGUgcGllY2VzIChpZiBhcHBsaWNhYmxlKTwvbGk+XHJcbiAgICAgICAgICAgICAgICA8L3VsPlxyXG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBtdC0zXCI+VGhlc2UgZXhjZXB0aW9ucyB3aWxsIGJlIGNsZWFybHkgc3RhdGVkIG9uIHRoZWlyIHJlc3BlY3RpdmUgcHJvZHVjdCBwYWdlcy48L3A+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICBcclxuICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cImZvbnQtc2VyaWYgdGV4dC0yeGwgdGV4dC1bIzJjMmMyN10gbXQtMTIgbWItNCBmbGV4IGl0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImgtcHggdy0xMCBiZy1bIzhhODc3OF0gbXItNFwiPjwvc3Bhbj5cclxuICAgICAgICAgICAgICBFeGNoYW5nZSBQcm9jZXNzXHJcbiAgICAgICAgICAgIDwvaDI+XHJcbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm1iLTZcIj5cclxuICAgICAgICAgICAgICBUbyBzdGFydCBhbiBleGNoYW5nZSwgc2ltcGx5IGNvbnRhY3QgdXMgdmlhIFdoYXRzQXBwLCBJbnN0YWdyYW0sIG9yIG91ciB3ZWJzaXRlIHBvcnRhbC4gT3VyIHRlYW0gd2lsbCBndWlkZSB5b3UgdGhyb3VnaCB0aGUgcmV0dXJuIHByb2Nlc3MuXHJcbiAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgXHJcbiAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJmb250LXNlcmlmIHRleHQtMnhsIHRleHQtWyMyYzJjMjddIG10LTEyIG1iLTQgZmxleCBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJoLXB4IHctMTAgYmctWyM4YTg3NzhdIG1yLTRcIj48L3NwYW4+XHJcbiAgICAgICAgICAgICAgQU5LS09SIFBvaW50c1xyXG4gICAgICAgICAgICA8L2gyPlxyXG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtYi02XCI+XHJcbiAgICAgICAgICAgICAgSWYgeW91IGNob29zZSB0byByZWNlaXZlIEFOS0tPUiBQb2ludHMgaW5zdGVhZCBvZiBhIGRpcmVjdCBleGNoYW5nZSwgdGhlc2UgcG9pbnRzIHdpbGwgYmUgY3JlZGl0ZWQgdG8geW91ciBhY2NvdW50IG9uY2Ugd2UgcmVjZWl2ZSBhbmQgaW5zcGVjdCB5b3VyIHJldHVybmVkIGl0ZW0uIFBvaW50cyBoYXZlIHRoZSBzYW1lIHZhbHVlIGFzIEluZGlhbiBSdXBlZXMgYW5kIGNhbiBiZSB1c2VkIGZvciBmdXR1cmUgcHVyY2hhc2VzIG9uIG91ciB3ZWJzaXRlLlxyXG4gICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgIFxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLVsjZjRmM2YwXSBwLTggYm9yZGVyLWwtNCBib3JkZXItWyM4YTg3NzhdIG15LTEyXCI+XHJcbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZvbnQtc2VyaWYgdGV4dC14bCB0ZXh0LVsjMmMyYzI3XSBtYi0zXCI+UXVhbGl0eSBBc3N1cmFuY2U8L2gzPlxyXG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm1iLTBcIj5cclxuICAgICAgICAgICAgICAgIEVhY2ggQU5LS09SIHByb2R1Y3QgdW5kZXJnb2VzIHJpZ29yb3VzIHF1YWxpdHkgY2hlY2tzIGJlZm9yZSBkaXNwYXRjaC4gSWYgeW91IHJlY2VpdmUgYSBwcm9kdWN0IHdpdGggbWFudWZhY3R1cmluZyBkZWZlY3RzLCBwbGVhc2UgY29udGFjdCB1cyBpbW1lZGlhdGVseSB3aXRoIHBob3RvcyBvZiB0aGUgaXNzdWUsIGFuZCB3ZSdsbCBwcmlvcml0aXplIHlvdXIgZXhjaGFuZ2UuXHJcbiAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgXHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBtZDpmbGV4LXJvdyBtZDpqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIG10LTE2IHB0LTggYm9yZGVyLXQgYm9yZGVyLVsjZTVlMmQ5XVwiPlxyXG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1bIzhhODc3OF0gbWItNCBtZDptYi0wXCI+TGFzdCBVcGRhdGVkOiBBcHJpbCAyMDI1PC9wPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTRcIj5cclxuICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvdGVybXMtb2Ytc2VydmljZVwiIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1bIzJjMmMyN10gaG92ZXI6dGV4dC1bIzhhODc3OF0gdHJhbnNpdGlvbi1jb2xvcnNcIj5cclxuICAgICAgICAgICAgICAgICAgVGVybXMgb2YgU2VydmljZVxyXG4gICAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9wcml2YWN5LXBvbGljeVwiIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1bIzJjMmMyN10gaG92ZXI6dGV4dC1bIzhhODc3OF0gdHJhbnNpdGlvbi1jb2xvcnNcIj5cclxuICAgICAgICAgICAgICAgICAgUHJpdmFjeSBQb2xpY3lcclxuICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvc2hpcHBpbmctcG9saWN5XCIgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LVsjMmMyYzI3XSBob3Zlcjp0ZXh0LVsjOGE4Nzc4XSB0cmFuc2l0aW9uLWNvbG9yc1wiPlxyXG4gICAgICAgICAgICAgICAgICBTaGlwcGluZyBQb2xpY3lcclxuICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L21vdGlvbi5kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufSAiXSwibmFtZXMiOlsiUmVhY3QiLCJtb3Rpb24iLCJMaW5rIiwiUmVmcmVzaEN3IiwiUGFja2FnZSIsIkFsZXJ0Q2lyY2xlIiwiUmV0dXJuUG9saWN5UGFnZSIsImRpdiIsImNsYXNzTmFtZSIsImluaXRpYWwiLCJvcGFjaXR5IiwieSIsImFuaW1hdGUiLCJ0cmFuc2l0aW9uIiwiZHVyYXRpb24iLCJlYXNlIiwiaDEiLCJwIiwiZGVsYXkiLCJoMiIsInNwYW4iLCJoMyIsInVsIiwibGkiLCJocmVmIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/return-policy/page.tsx\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["framework-node_modules_next_dist_a","framework-node_modules_next_dist_client_a","framework-node_modules_next_dist_client_components_ap","framework-node_modules_next_dist_client_components_b","framework-node_modules_next_dist_client_components_layout-router_js-4906aef6","framework-node_modules_next_dist_client_components_m","framework-node_modules_next_dist_client_components_p","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_C","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_LeftRightDi-d5fdd2e0","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_O","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_Overlay_mai-e776ae3b","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_Te","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_components_V","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_container_B","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_container_R","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_helpers_f","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_helpers_h","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_h","framework-node_modules_next_dist_client_components_react-dev-overlay_internal_styles_B","framework-node_modules_next_dist_client_components_rea","framework-node_modules_next_dist_client_components_re","framework-node_modules_next_dist_client_components_router-reducer_co","framework-node_modules_next_dist_client_components_router-reducer_fe","framework-node_modules_next_dist_client_components_router-reducer_h","framework-node_modules_next_dist_client_components_router-reducer_pp","framework-node_modules_next_dist_client_components_router-reducer_reducers_f","framework-node_modules_next_dist_client_components_router-reducer_reducers_r","framework-node_modules_next_dist_client_components_router-reducer_r","framework-node_modules_next_dist_client_c","framework-node_modules_next_dist_client_g","framework-node_modules_next_dist_client_l","framework-node_modules_next_dist_compiled_a","framework-node_modules_next_dist_compiled_m","framework-node_modules_next_dist_compiled_react-dom_cjs_react-dom_development_js-3041f41d","framework-node_modules_next_dist_compiled_react-d","framework-node_modules_next_dist_compiled_react-server-dom-webpack_cjs_react-server-dom-webpack-clie-4912d8da","framework-node_modules_next_dist_compiled_react_cjs_react-jsx-dev-runtime_development_js-12999a20","framework-node_modules_next_dist_compiled_react_c","framework-node_modules_next_dist_compiled_react_cjs_react_development_js-a784779d","framework-node_modules_next_dist_compiled_r","framework-node_modules_next_dist_l","framework-node_modules_next_dist_shared_lib_a","framework-node_modules_next_dist_shared_lib_ha","framework-node_modules_next_dist_shared_lib_h","framework-node_modules_next_dist_shared_lib_lazy-dynamic_b","framework-node_modules_next_dist_shared_lib_m","framework-node_modules_next_dist_shared_lib_router-","framework-node_modules_next_dist_shared_lib_router_utils_o","framework-node_modules_next_dist_shared_lib_r","framework-node_modules_next_d","framework-node_modules_next_font_google_target_css-0","commons-_","commons-node_modules_framer-motion_dist_es_animation_animators_i","commons-node_modules_framer-motion_dist_es_a","commons-node_modules_framer-motion_dist_es_d","commons-node_modules_framer-motion_dist_es_motion_f","commons-node_modules_framer-motion_dist_es_projection_a","commons-node_modules_framer-motion_dist_es_projection_node_create-projection-node_mjs-d9cf742e","commons-node_modules_framer-motion_dist_es_render_VisualElement_mjs-19d9658a","commons-node_modules_framer-motion_dist_es_render_d","commons-node_modules_framer-motion_dist_es_r","commons-node_modules_framer-motion_dist_es_value_i","commons-node_modules_go","commons-node_modules_graphql_language_a","commons-node_modules_graphql_language_parser_mjs-c45803c0","commons-node_modules_graphql_language_p","commons-node_modules_l","commons-node_modules_react-hook-form_dist_index_esm_mjs-74baa987","commons-node_modules_tailwind-merge_dist_bundle-mjs_mjs-a19ea93e","commons-node_modules_upstash_redis_chunk-5XANP4AV_mjs-ec81489a","commons-node_modules_zustand_esm_i","commons-src_components_auth_AuthForm_tsx-6dd93bd8","commons-src_components_c","commons-src_com","commons-src_lib_c","commons-src_lib_s","commons-src_lib_woocommerce_ts-ea0e4c9f","main-app"], function() { return __webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5Cankkorwoo%5C%5Cankkor%5C%5Csrc%5C%5Capp%5C%5Creturn-policy%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);