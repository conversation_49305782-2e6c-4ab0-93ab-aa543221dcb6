"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/page-src_app_page_tsx-8a0e3ed7"],{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Scissors_Shirt_ShoppingBag_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Scissors,Shirt,ShoppingBag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Scissors_Shirt_ShoppingBag_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Scissors,Shirt,ShoppingBag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Scissors_Shirt_ShoppingBag_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Scissors,Shirt,ShoppingBag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shirt.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Scissors_Shirt_ShoppingBag_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Scissors,Shirt,ShoppingBag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scissors.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Scissors_Shirt_ShoppingBag_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Scissors,Shirt,ShoppingBag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_product_ProductCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/product/ProductCard */ \"(app-pages-browser)/./src/components/product/ProductCard.tsx\");\n/* harmony import */ var _hooks_usePageLoading__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/usePageLoading */ \"(app-pages-browser)/./src/hooks/usePageLoading.ts\");\n/* harmony import */ var _lib_woocommerce__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/woocommerce */ \"(app-pages-browser)/./src/lib/woocommerce.ts\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _components_home_BannerSlider__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/home/<USER>/ \"(app-pages-browser)/./src/components/home/<USER>");\n/* harmony import */ var _components_home_NewsletterPopup__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/home/<USER>/ \"(app-pages-browser)/./src/components/home/<USER>");\n/* harmony import */ var _components_home_LaunchingSoon__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/home/<USER>/ \"(app-pages-browser)/./src/components/home/<USER>");\n/* harmony import */ var _lib_productUtils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/productUtils */ \"(app-pages-browser)/./src/lib/productUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [featuredProducts, setFeaturedProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch products from WooCommerce\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchProducts = async ()=>{\n            try {\n                console.log(\"Fetching products for homepage...\");\n                const products = await (0,_lib_woocommerce__WEBPACK_IMPORTED_MODULE_6__.getAllProducts)(8); // Fetch 8 products\n                if (!products || products.length === 0) {\n                    console.warn(\"No products returned from WooCommerce\");\n                    setError(\"We're experiencing technical difficulties. Please try again later.\");\n                    setIsLoading(false);\n                    return;\n                }\n                console.log(\"Fetched \".concat(products.length, \" products from WooCommerce\"));\n                // Normalize the products and sort by newest first\n                const normalizedProducts = products.map((product)=>{\n                    const normalizedProduct = (0,_lib_woocommerce__WEBPACK_IMPORTED_MODULE_6__.normalizeProduct)(product);\n                    // Ensure currencyCode is included for use with currency symbols\n                    if (normalizedProduct) {\n                        normalizedProduct.currencyCode = \"INR\"; // Default to INR or get from WooCommerce settings\n                    }\n                    return normalizedProduct;\n                }).filter(Boolean).sort((a, b)=>{\n                    var _a__originalWooProduct, _a__originalWooProduct1, _b__originalWooProduct, _b__originalWooProduct1;\n                    // Sort by newest first - compare IDs or creation dates\n                    const aDate = ((_a__originalWooProduct = a._originalWooProduct) === null || _a__originalWooProduct === void 0 ? void 0 : _a__originalWooProduct.dateCreated) || ((_a__originalWooProduct1 = a._originalWooProduct) === null || _a__originalWooProduct1 === void 0 ? void 0 : _a__originalWooProduct1.date_created) || a.id;\n                    const bDate = ((_b__originalWooProduct = b._originalWooProduct) === null || _b__originalWooProduct === void 0 ? void 0 : _b__originalWooProduct.dateCreated) || ((_b__originalWooProduct1 = b._originalWooProduct) === null || _b__originalWooProduct1 === void 0 ? void 0 : _b__originalWooProduct1.date_created) || b.id;\n                    // If we have actual dates, compare them\n                    if (aDate && bDate && aDate !== a.id && bDate !== b.id) {\n                        return new Date(bDate).getTime() - new Date(aDate).getTime();\n                    }\n                    // Fallback to ID comparison (higher IDs are typically newer)\n                    return b.id.localeCompare(a.id);\n                });\n                console.log(\"Normalized products:\", normalizedProducts);\n                setFeaturedProducts(normalizedProducts);\n                setIsLoading(false);\n            } catch (err) {\n                console.error(\"Error fetching products:\", err);\n                setError(\"We're experiencing technical difficulties. Please try again later.\");\n                setIsLoading(false);\n            }\n        };\n        fetchProducts();\n    }, []);\n    // Use the page loading hook\n    (0,_hooks_usePageLoading__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(isLoading, \"thread\");\n    // Enhanced animation variants\n    const fadeIn = {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.8,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    const staggerChildren = {\n        animate: {\n            transition: {\n                staggerChildren: 0.15\n            }\n        }\n    };\n    const slideIn = {\n        initial: {\n            opacity: 0,\n            x: -30\n        },\n        animate: {\n            opacity: 1,\n            x: 0,\n            transition: {\n                duration: 0.9,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    const scaleIn = {\n        initial: {\n            opacity: 0,\n            scale: 0.95\n        },\n        animate: {\n            opacity: 1,\n            scale: 1,\n            transition: {\n                duration: 0.9,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-[#f8f8f5]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_home_LaunchingSoon__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_home_NewsletterPopup__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_home_BannerSlider__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"pt-36 pb-20 px-4 bg-gradient-to-b from-[#f4f3f0] to-[#f8f8f5]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto flex flex-col lg:flex-row items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                            className: \"lg:w-1/2\",\n                            variants: slideIn,\n                            initial: \"initial\",\n                            animate: \"animate\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-[#8a8778] text-lg mb-5 font-light tracking-widest uppercase\",\n                                    children: \"Timeless Distinction\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl md:text-6xl font-serif font-bold leading-tight mb-8 text-[#2c2c27]\",\n                                    children: [\n                                        \"Elevated essentials \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 35\n                                        }, this),\n                                        \"for the discerning gentleman\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-[#5c5c52] mb-8 leading-relaxed max-w-lg\",\n                                    children: \"Impeccably tailored garments crafted from the finest materials, designed to stand the test of time with understated elegance.\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    onClick: ()=>{},\n                                    className: \"bg-[#2c2c27] text-[#f4f3f0] px-10 py-4 hover:bg-[#3d3d35] transition-colors flex items-center gap-3 text-sm tracking-wider uppercase font-medium\",\n                                    whileHover: {\n                                        scale: 1.02\n                                    },\n                                    whileTap: {\n                                        scale: 0.98\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/collection\",\n                                        children: [\n                                            \"Explore Collection\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-block text-lg font-light\",\n                                                children: \"→\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                            className: \"lg:w-1/2 mt-12 lg:mt-0 relative\",\n                            variants: scaleIn,\n                            initial: \"initial\",\n                            animate: \"animate\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -z-10 top-0 right-0 w-80 h-80 bg-[#e0ddd3] rounded-full opacity-40 blur-3xl\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -z-10 bottom-0 right-20 w-64 h-64 bg-[#d5d0c3] rounded-full opacity-30 blur-3xl\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    src: \"/hero.jpg\",\n                                    alt: \"Ankkor Classic Style\",\n                                    width: 600,\n                                    height: 800,\n                                    className: \"rounded-sm shadow-lg relative z-10 image-animate border border-[#e5e2d9]\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -bottom-6 -left-6 bg-[#2c2c27] text-[#f4f3f0] py-4 px-8 text-sm tracking-wider uppercase z-20 hidden md:block\",\n                                    children: \"Est. 2025\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.section, {\n                className: \"py-24 px-4\",\n                initial: \"initial\",\n                whileInView: \"animate\",\n                viewport: {\n                    once: true\n                },\n                variants: staggerChildren,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-serif font-bold mb-5 text-[#2c2c27]\",\n                                    children: \"Signature Pieces\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-[#5c5c52] max-w-2xl mx-auto\",\n                                    children: \"Our most distinguished garments, selected for their exceptional quality and timeless appeal\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this),\n                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8\",\n                            children: Array.from({\n                                length: 8\n                            }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-pulse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"w-full h-80 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"w-3/4 h-4 mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"w-1/2 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, this),\n                        error && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-md mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-serif text-[#2c2c27] mb-4\",\n                                        children: \"Service Temporarily Unavailable\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-[#5c5c52] mb-6\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>window.location.reload(),\n                                        className: \"bg-[#2c2c27] text-white px-6 py-2 hover:bg-[#3d3d35] transition-colors\",\n                                        children: \"Try Again\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 13\n                        }, this),\n                        !isLoading && !error && featuredProducts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8\",\n                            children: featuredProducts.map((product)=>{\n                                var _product_priceRange_minVariantPrice, _product_priceRange, _product_images_;\n                                const originalProduct = product._originalWooProduct;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_product_ProductCard__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    id: product.id,\n                                    name: product.title,\n                                    price: (originalProduct === null || originalProduct === void 0 ? void 0 : originalProduct.salePrice) || (originalProduct === null || originalProduct === void 0 ? void 0 : originalProduct.price) || ((_product_priceRange = product.priceRange) === null || _product_priceRange === void 0 ? void 0 : (_product_priceRange_minVariantPrice = _product_priceRange.minVariantPrice) === null || _product_priceRange_minVariantPrice === void 0 ? void 0 : _product_priceRange_minVariantPrice.amount) || \"0\",\n                                    image: ((_product_images_ = product.images[0]) === null || _product_images_ === void 0 ? void 0 : _product_images_.url) || \"\",\n                                    slug: product.handle,\n                                    material: (0,_lib_woocommerce__WEBPACK_IMPORTED_MODULE_6__.getMetafield)(product, \"custom_material\", undefined, product.vendor || \"Premium Fabric\"),\n                                    isNew: true,\n                                    stockStatus: (originalProduct === null || originalProduct === void 0 ? void 0 : originalProduct.stockStatus) || \"IN_STOCK\",\n                                    compareAtPrice: product.compareAtPrice,\n                                    regularPrice: originalProduct === null || originalProduct === void 0 ? void 0 : originalProduct.regularPrice,\n                                    salePrice: originalProduct === null || originalProduct === void 0 ? void 0 : originalProduct.salePrice,\n                                    onSale: (originalProduct === null || originalProduct === void 0 ? void 0 : originalProduct.onSale) || false,\n                                    currencySymbol: (0,_lib_productUtils__WEBPACK_IMPORTED_MODULE_11__.getCurrencySymbol)(product.currencyCode),\n                                    currencyCode: product.currencyCode || \"INR\",\n                                    shortDescription: originalProduct === null || originalProduct === void 0 ? void 0 : originalProduct.shortDescription,\n                                    type: originalProduct === null || originalProduct === void 0 ? void 0 : originalProduct.type\n                                }, product.id, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 13\n                        }, this),\n                        !isLoading && !error && featuredProducts.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-[#5c5c52]\",\n                                children: \"No products available at the moment.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mt-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/collection\",\n                                className: \"inline-flex items-center text-[#2c2c27] hover:text-[#8a8778] transition-colors\",\n                                children: [\n                                    \"View Full Collection\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Scissors_Shirt_ShoppingBag_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"ml-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 171,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.section, {\n                className: \"py-24 px-4 bg-[#f4f3f0]\",\n                initial: \"initial\",\n                whileInView: \"animate\",\n                viewport: {\n                    once: true\n                },\n                variants: staggerChildren,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto max-w-6xl flex flex-col lg:flex-row items-center gap-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                            className: \"lg:w-1/2 relative\",\n                            variants: fadeIn,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -inset-4 border border-[#8a8778] -z-10 opacity-40\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    src: \"https://images.unsplash.com/photo-1596755094514-f87e34085b2c?q=80&w=2944&auto=format&fit=crop\",\n                                    alt: \"Ankkor Premium Shirt\",\n                                    width: 600,\n                                    height: 600,\n                                    sizes: \"(max-width: 768px) 100vw, 600px\",\n                                    className: \"w-full h-[600px] object-cover image-animate border border-[#e5e2d9] transition-all duration-700\",\n                                    priority: true\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -bottom-6 -right-6 bg-[#2c2c27] text-[#f4f3f0] py-3 px-6 text-xs tracking-widest uppercase\",\n                                    children: \"Est. 2025\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                            className: \"lg:w-1/2 space-y-8\",\n                            variants: fadeIn,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-px w-12 bg-[#8a8778]\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-[#8a8778] text-sm tracking-widest uppercase\",\n                                            children: \"Our Heritage\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 336,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-serif font-bold text-[#2c2c27] mb-8\",\n                                    children: \"ANKKOR – The Anchor of Timeless Style\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6 text-[#5c5c52] leading-relaxed\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Founded in 2025, ANKKOR emerged with a vision: to revive the elegance of classic, old money formals and make them a part of everyday wear. In a world of fleeting fashion, ANKKOR stands still—rooted in sophistication, purpose, and grace.\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-px w-full bg-[#e5e2d9]\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"The name ANKKOR draws strength from the anchor—symbolizing durability, stability, and timeless resilience. Just like an anchor holds firm amidst shifting tides, our brand is grounded in the belief that true style never fades. This philosophy carries through in every stitch of our garments—crafted with high-quality threads that mirror the strength and integrity of the symbol we stand by.\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Our designs speak the language of quiet luxury: clean cuts, refined fabrics, and enduring silhouettes. ANKKOR is for those who appreciate the subtle power of understated elegance and the confidence it brings.\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Every piece we create is a commitment—to strength, to style, and to the timeless values of classic fashion.\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-serif text-xl text-[#2c2c27] italic\",\n                                            children: \"ANKKOR – Where class meets character. Wear timeless. Be anchored.\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-10\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/about\",\n                                                className: \"inline-flex items-center px-6 py-3 text-sm font-medium text-[#2c2c27] border border-[#2c2c27] hover:bg-[#2c2c27] hover:text-white transition-colors\",\n                                                children: [\n                                                    \"Read more\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        fill: \"none\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        height: \"15px\",\n                                                        width: \"15px\",\n                                                        className: \"ml-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinejoin: \"round\",\n                                                            strokeLinecap: \"round\",\n                                                            strokeMiterlimit: 10,\n                                                            strokeWidth: \"1.5\",\n                                                            stroke: \"currentColor\",\n                                                            d: \"M8.91016 19.9201L15.4302 13.4001C16.2002 12.6301 16.2002 11.3701 15.4302 10.6001L8.91016 4.08008\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 373,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 310,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 303,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.section, {\n                className: \"py-24 px-4 bg-[#f0ede6]\",\n                initial: \"initial\",\n                whileInView: \"animate\",\n                viewport: {\n                    once: true\n                },\n                variants: staggerChildren,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto max-w-6xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                            className: \"text-center max-w-xl mx-auto mb-16\",\n                            variants: fadeIn,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-[#8a8778] text-sm mb-3 tracking-widest uppercase\",\n                                    children: \"Our Promise\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-serif font-bold mb-4 text-[#2c2c27]\",\n                                    children: \"The Ankkor Distinction\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 424,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-[#5c5c52]\",\n                                    children: \"We uphold the highest standards in every aspect of our craft, ensuring an exceptional experience with every garment\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 427,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 422,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                    className: \"text-center space-y-5\",\n                                    variants: fadeIn,\n                                    whileHover: {\n                                        y: -5\n                                    },\n                                    transition: {\n                                        duration: 0.3\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-[#faf9f6] w-20 h-20 mx-auto rounded-none flex items-center justify-center shadow-sm border border-[#e5e2d9]\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Scissors_Shirt_ShoppingBag_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-8 h-8 text-[#2c2c27]\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 440,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-serif font-semibold text-lg text-[#2c2c27]\",\n                                            children: \"Curated Selection\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-[#5c5c52] text-sm\",\n                                            children: \"Each piece is meticulously selected to ensure exceptional quality and enduring style\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 434,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                    className: \"text-center space-y-5\",\n                                    variants: fadeIn,\n                                    whileHover: {\n                                        y: -5\n                                    },\n                                    transition: {\n                                        duration: 0.3\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-[#faf9f6] w-20 h-20 mx-auto rounded-none flex items-center justify-center shadow-sm border border-[#e5e2d9]\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Scissors_Shirt_ShoppingBag_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-8 h-8 text-[#2c2c27]\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 455,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-serif font-semibold text-lg text-[#2c2c27]\",\n                                            children: \"Master Tailoring\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-[#5c5c52] text-sm\",\n                                            children: \"Precision craftsmanship ensuring impeccable fit, superior comfort, and distinctive character\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                    className: \"text-center space-y-5\",\n                                    variants: fadeIn,\n                                    whileHover: {\n                                        y: -5\n                                    },\n                                    transition: {\n                                        duration: 0.3\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-[#faf9f6] w-20 h-20 mx-auto rounded-none flex items-center justify-center shadow-sm border border-[#e5e2d9]\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Scissors_Shirt_ShoppingBag_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-8 h-8 text-[#2c2c27]\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-serif font-semibold text-lg text-[#2c2c27]\",\n                                            children: \"Exceptional Materials\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-[#5c5c52] text-sm\",\n                                            children: \"Sourced from heritage mills with centuries of tradition and uncompromising standards\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 464,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                    className: \"text-center space-y-5\",\n                                    variants: fadeIn,\n                                    whileHover: {\n                                        y: -5\n                                    },\n                                    transition: {\n                                        duration: 0.3\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-[#faf9f6] w-20 h-20 mx-auto rounded-none flex items-center justify-center shadow-sm border border-[#e5e2d9]\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Scissors_Shirt_ShoppingBag_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-8 h-8 text-[#2c2c27]\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-serif font-semibold text-lg text-[#2c2c27]\",\n                                            children: \"Client Dedication\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-[#5c5c52] text-sm\",\n                                            children: \"Personalized attention and service that honors the tradition of bespoke craftsmanship\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 489,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 479,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 433,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 421,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 414,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.section, {\n                className: \"py-24 px-4 bg-[#2c2c27] text-[#f4f3f0] relative overflow-hidden\",\n                initial: {\n                    opacity: 0\n                },\n                whileInView: {\n                    opacity: 1\n                },\n                viewport: {\n                    once: true\n                },\n                transition: {\n                    duration: 0.8\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-[#8a8778] to-transparent opacity-40\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 505,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"container mx-auto max-w-4xl text-center relative z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl font-serif font-bold mb-6\",\n                                children: \"Experience Ankkor\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 507,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-[#d5d0c3] mb-10 max-w-2xl mx-auto\",\n                                children: \"Visit our flagship boutique for a personalized styling consultation with our master tailors, or explore our collection online.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 508,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/collection\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.button, {\n                                    className: \"border border-[#8a8778] text-[#f4f3f0] px-10 py-4 hover:bg-[#3d3d35] transition-colors text-sm tracking-wider uppercase font-medium\",\n                                    whileHover: {\n                                        scale: 1.02\n                                    },\n                                    whileTap: {\n                                        scale: 0.98\n                                    },\n                                    children: \"Shop the Collection\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 513,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 512,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 506,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 right-0 w-full h-px bg-gradient-to-r from-transparent via-[#8a8778] to-transparent opacity-40\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 522,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 498,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"16Idl6PK0JKYVPWt1SVhtOApV+U=\", false, function() {\n    return [\n        _hooks_usePageLoading__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

}]);