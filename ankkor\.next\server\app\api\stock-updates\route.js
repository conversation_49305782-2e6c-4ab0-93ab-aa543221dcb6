"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/stock-updates/route";
exports.ids = ["app/api/stock-updates/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstock-updates%2Froute&page=%2Fapi%2Fstock-updates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstock-updates%2Froute.ts&appDir=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cankkorwoo%5Cankkor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstock-updates%2Froute&page=%2Fapi%2Fstock-updates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstock-updates%2Froute.ts&appDir=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cankkorwoo%5Cankkor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var E_ankkorwoo_ankkor_src_app_api_stock_updates_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/stock-updates/route.ts */ \"(rsc)/./src/app/api/stock-updates/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/stock-updates/route\",\n        pathname: \"/api/stock-updates\",\n        filename: \"route\",\n        bundlePath: \"app/api/stock-updates/route\"\n    },\n    resolvedPagePath: \"E:\\\\ankkorwoo\\\\ankkor\\\\src\\\\app\\\\api\\\\stock-updates\\\\route.ts\",\n    nextConfigOutput,\n    userland: E_ankkorwoo_ankkor_src_app_api_stock_updates_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/stock-updates/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhcGklMkZzdG9jay11cGRhdGVzJTJGcm91dGUmcGFnZT0lMkZhcGklMkZzdG9jay11cGRhdGVzJTJGcm91dGUmYXBwUGF0aHM9JnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGc3RvY2stdXBkYXRlcyUyRnJvdXRlLnRzJmFwcERpcj1FJTNBJTVDYW5ra29yd29vJTVDYW5ra29yJTVDc3JjJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1FJTNBJTVDYW5ra29yd29vJTVDYW5ra29yJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFBc0c7QUFDdkM7QUFDYztBQUNhO0FBQzFGO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixnSEFBbUI7QUFDM0M7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxZQUFZO0FBQ1osQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLFFBQVEsaUVBQWlFO0FBQ3pFO0FBQ0E7QUFDQSxXQUFXLDRFQUFXO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDdUg7O0FBRXZIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYW5ra29yLz9lMWQ5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFwcFJvdXRlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgcGF0Y2hGZXRjaCBhcyBfcGF0Y2hGZXRjaCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9wYXRjaC1mZXRjaFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIkU6XFxcXGFua2tvcndvb1xcXFxhbmtrb3JcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcc3RvY2stdXBkYXRlc1xcXFxyb3V0ZS50c1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvc3RvY2stdXBkYXRlcy9yb3V0ZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL3N0b2NrLXVwZGF0ZXNcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL3N0b2NrLXVwZGF0ZXMvcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCJFOlxcXFxhbmtrb3J3b29cXFxcYW5ra29yXFxcXHNyY1xcXFxhcHBcXFxcYXBpXFxcXHN0b2NrLXVwZGF0ZXNcXFxccm91dGUudHNcIixcbiAgICBuZXh0Q29uZmlnT3V0cHV0LFxuICAgIHVzZXJsYW5kXG59KTtcbi8vIFB1bGwgb3V0IHRoZSBleHBvcnRzIHRoYXQgd2UgbmVlZCB0byBleHBvc2UgZnJvbSB0aGUgbW9kdWxlLiBUaGlzIHNob3VsZFxuLy8gYmUgZWxpbWluYXRlZCB3aGVuIHdlJ3ZlIG1vdmVkIHRoZSBvdGhlciByb3V0ZXMgdG8gdGhlIG5ldyBmb3JtYXQuIFRoZXNlXG4vLyBhcmUgdXNlZCB0byBob29rIGludG8gdGhlIHJvdXRlLlxuY29uc3QgeyByZXF1ZXN0QXN5bmNTdG9yYWdlLCBzdGF0aWNHZW5lcmF0aW9uQXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcyB9ID0gcm91dGVNb2R1bGU7XG5jb25zdCBvcmlnaW5hbFBhdGhuYW1lID0gXCIvYXBpL3N0b2NrLXVwZGF0ZXMvcm91dGVcIjtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgc2VydmVySG9va3MsXG4gICAgICAgIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCByZXF1ZXN0QXN5bmNTdG9yYWdlLCBzdGF0aWNHZW5lcmF0aW9uQXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgb3JpZ2luYWxQYXRobmFtZSwgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstock-updates%2Froute&page=%2Fapi%2Fstock-updates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstock-updates%2Froute.ts&appDir=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cankkorwoo%5Cankkor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/stock-updates/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/stock-updates/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _upstash_redis__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @upstash/redis */ \"(rsc)/./node_modules/@upstash/redis/nodejs.mjs\");\n\n\n// Initialize Redis with fallback handling\nconst redis = process.env.UPSTASH_REDIS_REST_URL && process.env.UPSTASH_REDIS_REST_TOKEN ? new _upstash_redis__WEBPACK_IMPORTED_MODULE_1__.Redis({\n    url: process.env.UPSTASH_REDIS_REST_URL,\n    token: process.env.UPSTASH_REDIS_REST_TOKEN\n}) : null;\n// Server-Sent Events endpoint for real-time stock updates\nasync function GET(request) {\n    const { searchParams } = new URL(request.url);\n    const productIds = searchParams.get(\"products\")?.split(\",\") || [];\n    // Set up SSE headers\n    const headers = new Headers({\n        \"Content-Type\": \"text/event-stream\",\n        \"Cache-Control\": \"no-cache\",\n        \"Connection\": \"keep-alive\",\n        \"Access-Control-Allow-Origin\": \"*\",\n        \"Access-Control-Allow-Headers\": \"Cache-Control\"\n    });\n    // Create a readable stream for SSE\n    const stream = new ReadableStream({\n        start (controller) {\n            // Send initial connection message\n            controller.enqueue(`data: ${JSON.stringify({\n                type: \"connected\",\n                message: \"Stock updates stream connected\",\n                timestamp: new Date().toISOString()\n            })}\\n\\n`);\n            // Function to check for stock updates\n            const checkStockUpdates = async ()=>{\n                if (!redis) {\n                    console.log(\"Redis not available, skipping stock update check\");\n                    return;\n                }\n                try {\n                    for (const productId of productIds){\n                        const stockUpdate = await redis.get(`stock_update:${productId}`);\n                        if (stockUpdate) {\n                            // Send stock update to client\n                            controller.enqueue(`data: ${JSON.stringify({\n                                type: \"stock_update\",\n                                productId,\n                                ...stockUpdate,\n                                timestamp: new Date().toISOString()\n                            })}\\n\\n`);\n                            // Remove the update after sending (optional)\n                            await redis.del(`stock_update:${productId}`);\n                        }\n                    }\n                } catch (error) {\n                    console.error(\"Error checking stock updates:\", error);\n                    // Send error notification to client\n                    controller.enqueue(`data: ${JSON.stringify({\n                        type: \"error\",\n                        message: \"Stock update service temporarily unavailable\",\n                        timestamp: new Date().toISOString()\n                    })}\\n\\n`);\n                }\n            };\n            // Check for updates every 5 seconds (only if Redis is available)\n            let interval = null;\n            if (redis) {\n                interval = setInterval(checkStockUpdates, 5000);\n                console.log(\"Started stock updates polling (Redis available)\");\n            } else {\n                console.log(\"Redis not available, stock updates polling disabled\");\n                // Send notification to client that real-time updates are not available\n                controller.enqueue(`data: ${JSON.stringify({\n                    type: \"service_unavailable\",\n                    message: \"Real-time stock updates are currently unavailable\",\n                    timestamp: new Date().toISOString()\n                })}\\n\\n`);\n            }\n            // Send heartbeat every 30 seconds to keep connection alive\n            const heartbeat = setInterval(()=>{\n                controller.enqueue(`data: ${JSON.stringify({\n                    type: \"heartbeat\",\n                    timestamp: new Date().toISOString()\n                })}\\n\\n`);\n            }, 30000);\n            // Cleanup function\n            const cleanup = ()=>{\n                if (interval) clearInterval(interval);\n                clearInterval(heartbeat);\n                controller.close();\n            };\n            // Handle client disconnect\n            request.signal.addEventListener(\"abort\", cleanup);\n            // Auto-cleanup after 5 minutes\n            setTimeout(cleanup, 5 * 60 * 1000);\n        }\n    });\n    return new Response(stream, {\n        headers\n    });\n}\n// POST endpoint to manually trigger stock update notifications\nasync function POST(request) {\n    try {\n        const { productId, stockData } = await request.json();\n        if (!productId || !stockData) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"productId and stockData are required\"\n            }, {\n                status: 400\n            });\n        }\n        // Store stock update for SSE to pick up\n        if (redis) {\n            await redis.set(`stock_update:${productId}`, stockData, 60 // 1 minute TTL\n            );\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: \"Stock update notification sent\",\n            productId,\n            timestamp: new Date().toISOString()\n        });\n    } catch (error) {\n        console.error(\"Error sending stock update notification:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to send stock update notification\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/stock-updates/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@upstash","vendor-chunks/crypto-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstock-updates%2Froute&page=%2Fapi%2Fstock-updates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstock-updates%2Froute.ts&appDir=E%3A%5Cankkorwoo%5Cankkor%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5Cankkorwoo%5Cankkor&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();